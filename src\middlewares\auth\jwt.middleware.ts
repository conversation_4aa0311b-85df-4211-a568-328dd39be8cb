import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { APIRequest, AuthUser, JWTPayload } from '../../types/api';
import { UnauthorizedError } from '../../utils/errors/unauthorized.error';
import { ForbiddenError } from '../../utils/errors/forbidden.error';
import { AdminRepository } from '../../repositories/website/AdminRepository';
import { CacheService } from '../../config/redis';

export class JWTMiddleware {
  private adminRepository: AdminRepository;
  private cacheService: CacheService;
  private jwtSecret: string;
  private jwtExpiresIn: string;

  constructor(
    adminRepository: AdminRepository,
    cacheService: CacheService,
    jwtSecret: string = process.env.JWT_SECRET!,
    jwtExpiresIn: string = process.env.JWT_EXPIRES_IN || '24h'
  ) {
    this.adminRepository = adminRepository;
    this.cacheService = cacheService;
    this.jwtSecret = jwtSecret;
    this.jwtExpiresIn = jwtExpiresIn;
  }

  // Generate JWT token
  generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn,
      issuer: process.env.JWT_ISSUER || 'fus-cms',
      audience: process.env.JWT_AUDIENCE || 'fus-cms-api',
    });
  }

  // Generate refresh token
  generateRefreshToken(userId: number): string {
    return jwt.sign(
      { sub: userId, type: 'refresh' },
      this.jwtSecret,
      {
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        issuer: process.env.JWT_ISSUER || 'fus-cms',
        audience: process.env.JWT_AUDIENCE || 'fus-cms-api',
      }
    );
  }

  // Verify JWT token
  verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.jwtSecret, {
        issuer: process.env.JWT_ISSUER || 'fus-cms',
        audience: process.env.JWT_AUDIENCE || 'fus-cms-api',
      }) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new UnauthorizedError('Token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new UnauthorizedError('Invalid token');
      } else {
        throw new UnauthorizedError('Token verification failed');
      }
    }
  }

  // Authentication middleware
  authenticate = async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedError('Missing or invalid authorization header');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      const payload = this.verifyToken(token);

      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedError('Token has been revoked');
      }

      // Get user from cache or database
      const user = await this.getUserFromPayload(payload);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      // Check if user is active
      if (user.status !== 1) {
        throw new UnauthorizedError('User account is inactive');
      }

      // Attach user to request
      req.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName || '',
        roles: user.roles || [],
        permissions: user.permissions || [],
      };

      next();
    } catch (error) {
      next(error);
    }
  };

  // Authorization middleware factory
  authorize = (requiredPermissions: string[] = [], requireAll: boolean = false) => {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        if (!req.user) {
          throw new UnauthorizedError('Authentication required');
        }

        // Super admin bypass
        if (req.user.roles.includes('super_admin')) {
          return next();
        }

        // Check permissions
        if (requiredPermissions.length > 0) {
          const userPermissions = req.user.permissions;
          
          const hasPermission = requireAll
            ? requiredPermissions.every(permission => userPermissions.includes(permission))
            : requiredPermissions.some(permission => userPermissions.includes(permission));

          if (!hasPermission) {
            throw new ForbiddenError('Insufficient permissions');
          }
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  };

  // Role-based authorization
  requireRole = (requiredRoles: string[] = [], requireAll: boolean = false) => {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        if (!req.user) {
          throw new UnauthorizedError('Authentication required');
        }

        const userRoles = req.user.roles;
        
        const hasRole = requireAll
          ? requiredRoles.every(role => userRoles.includes(role))
          : requiredRoles.some(role => userRoles.includes(role));

        if (!hasRole) {
          throw new ForbiddenError('Insufficient role permissions');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  };

  // Optional authentication (doesn't fail if no token)
  optionalAuth = async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next();
      }

      const token = authHeader.substring(7);
      
      try {
        const payload = this.verifyToken(token);
        const user = await this.getUserFromPayload(payload);
        
        if (user && user.status === 1) {
          req.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName || '',
            roles: user.roles || [],
            permissions: user.permissions || [],
          };
        }
      } catch (error) {
        // Ignore token errors for optional auth
      }

      next();
    } catch (error) {
      next(error);
    }
  };

  // Blacklist token
  async blacklistToken(token: string, expiresIn?: number): Promise<void> {
    try {
      const payload = this.verifyToken(token);
      const ttl = expiresIn || (payload.exp! - Math.floor(Date.now() / 1000));
      
      await this.cacheService.set(`blacklist:${token}`, true, ttl);
    } catch (error) {
      // Token might be invalid, but we still want to blacklist it
      const defaultTtl = expiresIn || 86400; // 24 hours
      await this.cacheService.set(`blacklist:${token}`, true, defaultTtl);
    }
  }

  // Check if token is blacklisted
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklisted = await this.cacheService.get(`blacklist:${token}`);
      return blacklisted === true;
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false;
    }
  }

  // Get user from JWT payload
  private async getUserFromPayload(payload: JWTPayload): Promise<any> {
    try {
      // Try cache first
      const cacheKey = `user:${payload.sub}`;
      const cachedUser = await this.cacheService.get(cacheKey);
      
      if (cachedUser) {
        return cachedUser;
      }

      // Get from database
      const user = await this.adminRepository.findByIdWithRolesAndPermissions(payload.sub);
      
      if (user) {
        // Cache for 15 minutes
        await this.cacheService.set(cacheKey, user, 900);
      }

      return user;
    } catch (error) {
      console.error('Error getting user from payload:', error);
      return null;
    }
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const payload = jwt.verify(refreshToken, this.jwtSecret) as any;
      
      if (payload.type !== 'refresh') {
        throw new UnauthorizedError('Invalid refresh token');
      }

      const user = await this.getUserFromPayload({ sub: payload.sub } as JWTPayload);
      if (!user || user.status !== 1) {
        throw new UnauthorizedError('User not found or inactive');
      }

      // Generate new tokens
      const newAccessToken = this.generateToken({
        sub: user.id,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
      });

      const newRefreshToken = this.generateRefreshToken(user.id);

      // Blacklist old refresh token
      await this.blacklistToken(refreshToken);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new UnauthorizedError('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new UnauthorizedError('Invalid refresh token');
      } else {
        throw error;
      }
    }
  }

  // Logout (blacklist token)
  async logout(token: string): Promise<void> {
    await this.blacklistToken(token);
    
    // Also clear user cache
    try {
      const payload = this.verifyToken(token);
      await this.cacheService.del(`user:${payload.sub}`);
    } catch (error) {
      // Ignore errors when clearing cache
    }
  }
}
