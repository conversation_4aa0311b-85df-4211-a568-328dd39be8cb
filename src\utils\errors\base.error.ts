export abstract class APIError extends Error {
  public abstract readonly statusCode: number;
  public abstract readonly isOperational: boolean;
  public readonly errors?: Record<string, string>;

  constructor(message: string, errors?: Record<string, string>) {
    super(message);
    this.errors = errors;
    
    // Ensure the name of this error is the same as the class name
    this.name = this.constructor.name;
    
    // This clips the constructor invocation from the stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  public abstract toJSON(): object;
}
