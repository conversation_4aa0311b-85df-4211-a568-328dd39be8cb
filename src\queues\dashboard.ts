import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { queues } from './index';

// Create server adapter
export const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Create Bull Board with BullMQ adapters
const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: queues.map(queue => new BullMQAdapter(queue)),
  serverAdapter: serverAdapter,
});

// Export dashboard utilities
export {
  addQueue,
  removeQueue,
  setQueues,
  replaceQueues,
};

// Dashboard configuration
export const dashboardConfig = {
  basePath: '/admin/queues',
  uiConfig: {
    boardTitle: 'FUS CMS Queue Dashboard',
    boardLogo: {
      path: 'https://cdn.jsdelivr.net/npm/bullmq@1.91.1/src/assets/logo.svg',
      width: '100px',
      height: 'auto',
    },
    miscLinks: [
      { text: 'API Documentation', url: '/api/docs' },
      { text: 'Health Check', url: '/health-check' },
    ],
    favIcon: {
      default: 'static/images/logo.ico',
      alternative: 'static/images/logo.png',
    },
  },
};

// Queue statistics service
export class QueueDashboardService {
  static async getOverallStats() {
    const stats = {
      totalQueues: queues.length,
      totalJobs: 0,
      activeJobs: 0,
      waitingJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      delayedJobs: 0,
    };

    for (const queue of queues) {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      stats.activeJobs += active.length;
      stats.waitingJobs += waiting.length;
      stats.completedJobs += completed.length;
      stats.failedJobs += failed.length;
      stats.delayedJobs += delayed.length;
      stats.totalJobs += waiting.length + active.length + completed.length + failed.length + delayed.length;
    }

    return stats;
  }

  static async getQueueDetails(queueName: string) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const [waiting, active, completed, failed, delayed, isPaused] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
      queue.isPaused(),
    ]);

    return {
      name: queue.name,
      isPaused,
      counts: {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      },
      jobs: {
        waiting: waiting.slice(0, 10).map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          opts: job.opts,
          timestamp: job.timestamp,
        })),
        active: active.slice(0, 10).map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          processedOn: job.processedOn,
          progress: job.progress,
        })),
        failed: failed.slice(0, 10).map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          failedReason: job.failedReason,
          finishedOn: job.finishedOn,
          attemptsMade: job.attemptsMade,
        })),
      },
    };
  }

  static async getJobsByTimeRange(queueName: string, startTime: Date, endTime: Date) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const jobs = await queue.getJobs(['completed', 'failed'], 0, -1, true);
    
    return jobs.filter(job => {
      const jobTime = new Date(job.finishedOn || job.processedOn || job.timestamp);
      return jobTime >= startTime && jobTime <= endTime;
    }).map(job => ({
      id: job.id,
      name: job.name,
      data: job.data,
      status: job.finishedOn ? (job.failedReason ? 'failed' : 'completed') : 'active',
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      failedReason: job.failedReason,
      duration: job.finishedOn && job.processedOn ? job.finishedOn - job.processedOn : null,
    }));
  }

  static async getJobMetrics(queueName: string, period: 'hour' | 'day' | 'week' = 'day') {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const now = new Date();
    let startTime: Date;
    let groupBy: string;

    switch (period) {
      case 'hour':
        startTime = new Date(now.getTime() - 60 * 60 * 1000); // Last hour
        groupBy = 'minute';
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // Last week
        groupBy = 'day';
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Last day
        groupBy = 'hour';
    }

    const jobs = await this.getJobsByTimeRange(queueName, startTime, now);
    
    // Group jobs by time period
    const metrics: Record<string, { completed: number; failed: number; total: number }> = {};
    
    jobs.forEach(job => {
      const jobTime = new Date(job.finishedOn || job.processedOn || 0);
      let timeKey: string;

      switch (groupBy) {
        case 'minute':
          timeKey = `${jobTime.getHours()}:${jobTime.getMinutes().toString().padStart(2, '0')}`;
          break;
        case 'day':
          timeKey = jobTime.toISOString().split('T')[0];
          break;
        default:
          timeKey = `${jobTime.toISOString().split('T')[0]} ${jobTime.getHours()}:00`;
      }

      if (!metrics[timeKey]) {
        metrics[timeKey] = { completed: 0, failed: 0, total: 0 };
      }

      metrics[timeKey].total += 1;
      if (job.status === 'completed') {
        metrics[timeKey].completed += 1;
      } else if (job.status === 'failed') {
        metrics[timeKey].failed += 1;
      }
    });

    return {
      period,
      startTime: startTime.toISOString(),
      endTime: now.toISOString(),
      metrics,
    };
  }

  static async getFailureAnalysis(queueName: string, limit: number = 100) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const failedJobs = await queue.getFailed(0, limit - 1);
    
    const errorAnalysis: Record<string, { count: number; examples: any[] }> = {};
    
    failedJobs.forEach(job => {
      const errorType = job.failedReason?.split(':')[0] || 'Unknown Error';
      
      if (!errorAnalysis[errorType]) {
        errorAnalysis[errorType] = { count: 0, examples: [] };
      }
      
      errorAnalysis[errorType].count += 1;
      
      if (errorAnalysis[errorType].examples.length < 3) {
        errorAnalysis[errorType].examples.push({
          jobId: job.id,
          data: job.data,
          error: job.failedReason,
          failedAt: job.finishedOn,
          attempts: job.attemptsMade,
        });
      }
    });

    return {
      totalFailedJobs: failedJobs.length,
      errorTypes: Object.keys(errorAnalysis).length,
      analysis: errorAnalysis,
    };
  }
}

// Export router for Express integration
export const queueDashboardRouter = serverAdapter.getRouter();
