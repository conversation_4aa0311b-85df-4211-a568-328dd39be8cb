import { Worker, Job } from 'bullmq';
import { queueRedisClient } from '../../config/redis';

// Import job processors
import {
  processGameReport,
  handleGameReportJobFailure,
  handleGameReportJobCompletion,
  GameReportJobData,
} from '../jobs/gameReport.job';

import {
  processBannerReport,
  handleBannerReportJobFailure,
  handleBannerReportJobCompletion,
  BannerReportJobData,
} from '../jobs/bannerReport.job';

import {
  processLinkReport,
  handleLinkReportJobFailure,
  handleLinkReportJobCompletion,
  LinkReportJobData,
} from '../jobs/linkReport.job';

import {
  processLogEvent,
  handleLogEventJobFailure,
  handleLogEventJobCompletion,
  LogEventJobData,
} from '../jobs/logEvent.job';

import {
  processUpdateLocation,
  handleUpdateLocationJobFailure,
  handleUpdateLocationJobCompletion,
  UpdateLocationJobData,
} from '../jobs/updateLocation.job';

// Worker configuration
const workerConfig = {
  connection: queueRedisClient,
  concurrency: 5,
  removeOnComplete: 10,
  removeOnFail: 5,
};

// Game Report Worker
export const gameReportWorker = new Worker(
  'gameReport',
  async (job: Job<GameReportJobData>) => {
    return await processGameReport(job);
  },
  workerConfig
);

gameReportWorker.on('completed', handleGameReportJobCompletion);
gameReportWorker.on('failed', handleGameReportJobFailure);

// Banner Report Worker
export const bannerReportWorker = new Worker(
  'bannerReport',
  async (job: Job<BannerReportJobData>) => {
    return await processBannerReport(job);
  },
  workerConfig
);

bannerReportWorker.on('completed', handleBannerReportJobCompletion);
bannerReportWorker.on('failed', handleBannerReportJobFailure);

// Link Report Worker
export const linkReportWorker = new Worker(
  'linkReport',
  async (job: Job<LinkReportJobData>) => {
    return await processLinkReport(job);
  },
  workerConfig
);

linkReportWorker.on('completed', handleLinkReportJobCompletion);
linkReportWorker.on('failed', handleLinkReportJobFailure);

// Log Event Worker
export const logEventWorker = new Worker(
  'logEvent',
  async (job: Job<LogEventJobData>) => {
    return await processLogEvent(job);
  },
  {
    ...workerConfig,
    concurrency: 10, // Higher concurrency for logging
  }
);

logEventWorker.on('completed', handleLogEventJobCompletion);
logEventWorker.on('failed', handleLogEventJobFailure);

// Update Location Worker
export const updateLocationWorker = new Worker(
  'updateLocation',
  async (job: Job<UpdateLocationJobData>) => {
    return await processUpdateLocation(job);
  },
  workerConfig
);

updateLocationWorker.on('completed', handleUpdateLocationJobCompletion);
updateLocationWorker.on('failed', handleUpdateLocationJobFailure);

// Array of all workers for management
export const workers = [
  gameReportWorker,
  bannerReportWorker,
  linkReportWorker,
  logEventWorker,
  updateLocationWorker,
];

// Worker event handlers
workers.forEach((worker) => {
  worker.on('ready', () => {
    console.log(`✅ Worker ${worker.name} is ready`);
  });

  worker.on('error', (error) => {
    console.error(`❌ Worker ${worker.name} error:`, error);
  });

  worker.on('stalled', (jobId) => {
    console.warn(`⚠️ Worker ${worker.name} job ${jobId} stalled`);
  });

  worker.on('progress', (job, progress) => {
    console.log(`📊 Worker ${worker.name} job ${job.id} progress: ${progress}%`);
  });
});

// Graceful shutdown function
export const shutdownWorkers = async (): Promise<void> => {
  console.log('🔄 Shutting down workers...');
  
  const shutdownPromises = workers.map(async (worker) => {
    try {
      await worker.close();
      console.log(`✅ Worker ${worker.name} shut down successfully`);
    } catch (error) {
      console.error(`❌ Error shutting down worker ${worker.name}:`, error);
    }
  });

  await Promise.all(shutdownPromises);
  console.log('✅ All workers shut down');
};

// Start all workers
export const startWorkers = async (): Promise<void> => {
  console.log('🚀 Starting workers...');
  
  const startPromises = workers.map(async (worker) => {
    try {
      // Workers start automatically when created, but we can wait for them to be ready
      await new Promise((resolve) => {
        if (worker.isRunning()) {
          resolve(true);
        } else {
          worker.on('ready', resolve);
        }
      });
      console.log(`✅ Worker ${worker.name} started successfully`);
    } catch (error) {
      console.error(`❌ Error starting worker ${worker.name}:`, error);
    }
  });

  await Promise.all(startPromises);
  console.log('✅ All workers started');
};

// Health check for workers
export const getWorkersHealth = async (): Promise<Record<string, any>> => {
  const health: Record<string, any> = {};

  for (const worker of workers) {
    try {
      health[worker.name] = {
        isRunning: worker.isRunning(),
        isPaused: worker.isPaused(),
        isClosed: worker.isClosed(),
        concurrency: worker.opts.concurrency,
      };
    } catch (error) {
      health[worker.name] = {
        error: error instanceof Error ? error.message : 'Unknown error',
        isRunning: false,
      };
    }
  }

  return health;
};

// Pause all workers
export const pauseWorkers = async (): Promise<void> => {
  console.log('⏸️ Pausing workers...');
  
  const pausePromises = workers.map(async (worker) => {
    try {
      await worker.pause();
      console.log(`⏸️ Worker ${worker.name} paused`);
    } catch (error) {
      console.error(`❌ Error pausing worker ${worker.name}:`, error);
    }
  });

  await Promise.all(pausePromises);
  console.log('⏸️ All workers paused');
};

// Resume all workers
export const resumeWorkers = async (): Promise<void> => {
  console.log('▶️ Resuming workers...');
  
  const resumePromises = workers.map(async (worker) => {
    try {
      await worker.resume();
      console.log(`▶️ Worker ${worker.name} resumed`);
    } catch (error) {
      console.error(`❌ Error resuming worker ${worker.name}:`, error);
    }
  });

  await Promise.all(resumePromises);
  console.log('▶️ All workers resumed');
};
