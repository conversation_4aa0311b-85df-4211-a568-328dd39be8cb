import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GameService } from '../../../src/services/website/GameService';
import { GameRepository } from '../../../src/repositories/website/GameRepository';
import { FusGameRepository } from '../../../src/repositories/website/FusGameRepository';
import { CacheService } from '../../../src/config/redis';
import { QueueService } from '../../../src/queues';
import { BadRequestError } from '../../../src/utils/errors/badRequest.error';
import { NotFoundError } from '../../../src/utils/errors/notFound.error';

// Mock dependencies
vi.mock('../../../src/repositories/website/GameRepository');
vi.mock('../../../src/repositories/website/FusGameRepository');
vi.mock('../../../src/config/redis');
vi.mock('../../../src/queues');

describe('GameService', () => {
  let gameService: GameService;
  let mockGameRepository: vi.Mocked<GameRepository>;
  let mockFusGameRepository: vi.Mocked<FusGameRepository>;
  let mockCacheService: vi.Mocked<CacheService>;
  let mockQueueService: typeof QueueService;

  beforeEach(() => {
    // Create mocked instances
    mockGameRepository = new GameRepository({} as any) as vi.Mocked<GameRepository>;
    mockFusGameRepository = new FusGameRepository({} as any) as vi.Mocked<FusGameRepository>;
    mockCacheService = new CacheService() as vi.Mocked<CacheService>;
    mockQueueService = QueueService as vi.Mocked<typeof QueueService>;

    // Mock methods
    mockCacheService.get = vi.fn();
    mockCacheService.set = vi.fn();
    mockCacheService.invalidatePattern = vi.fn();
    mockGameRepository.findMany = vi.fn();
    mockGameRepository.findByCode = vi.fn();
    mockGameRepository.findByCodesInFid = vi.fn();
    mockGameRepository.create = vi.fn();
    mockFusGameRepository.createOrUpdate = vi.fn();
    mockQueueService.addGameReportJob = vi.fn();
    mockQueueService.addLogEventJob = vi.fn();

    gameService = new GameService(
      mockGameRepository,
      mockFusGameRepository,
      mockCacheService,
      mockQueueService
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getListGames', () => {
    it('should return cached games if available', async () => {
      const mockGames = [
        { id: 1, code: 'game1', name: 'Game 1', status: 1 },
        { id: 2, code: 'game2', name: 'Game 2', status: 1 },
      ];

      mockCacheService.get.mockResolvedValue(mockGames);

      const result = await gameService.getListGames({ status: 1 });

      expect(result).toEqual(mockGames);
      expect(mockCacheService.get).toHaveBeenCalledWith(
        expect.stringContaining('games:list')
      );
      expect(mockGameRepository.findMany).not.toHaveBeenCalled();
    });

    it('should fetch from repository if not cached', async () => {
      const mockGames = [
        { id: 1, code: 'game1', name: 'Game 1', status: 1 },
      ];

      mockCacheService.get.mockResolvedValue(null);
      mockGameRepository.findMany.mockResolvedValue(mockGames);

      const result = await gameService.getListGames({ status: 1 });

      expect(result).toEqual(mockGames);
      expect(mockGameRepository.findMany).toHaveBeenCalledWith(
        { status: 1 },
        expect.objectContaining({
          limit: 50,
          orderBy: expect.any(Object),
        })
      );
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('games:list'),
        mockGames,
        1800
      );
    });
  });

  describe('createGame', () => {
    it('should create games successfully', async () => {
      const gameData = {
        fid: 'test-fid',
        data: [
          { code: 'game1', name: 'Game 1' },
          { code: 'game2', name: 'Game 2' },
        ],
      };

      const mockFusGame = {
        id: 1,
        fid: 'test-fid',
        data: gameData.data,
        createdAt: new Date(),
      };

      mockGameRepository.findByCodesInFid.mockResolvedValue([]);
      mockGameRepository.create.mockResolvedValue({} as any);
      mockFusGameRepository.createOrUpdate.mockResolvedValue(mockFusGame);

      const result = await gameService.createGame(gameData);

      expect(result).toEqual(mockFusGame);
      expect(mockGameRepository.findByCodesInFid).toHaveBeenCalledWith(
        ['game1', 'game2'],
        'test-fid'
      );
      expect(mockGameRepository.create).toHaveBeenCalledTimes(2);
      expect(mockFusGameRepository.createOrUpdate).toHaveBeenCalledWith(gameData);
      expect(mockCacheService.invalidatePattern).toHaveBeenCalledWith('games:*');
    });

    it('should throw error if game codes already exist', async () => {
      const gameData = {
        fid: 'test-fid',
        data: [{ code: 'game1', name: 'Game 1' }],
      };

      const existingGames = [{ code: 'game1', name: 'Existing Game' }];
      mockGameRepository.findByCodesInFid.mockResolvedValue(existingGames as any);

      await expect(gameService.createGame(gameData)).rejects.toThrow(BadRequestError);
      expect(mockGameRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getGameDetail', () => {
    it('should return game detail from cache', async () => {
      const mockGame = { id: 1, code: 'game1', name: 'Game 1' };
      mockCacheService.get.mockResolvedValue(mockGame);

      const result = await gameService.getGameDetail('game1');

      expect(result).toEqual(mockGame);
      expect(mockCacheService.get).toHaveBeenCalledWith(
        expect.stringContaining('games:detail:game1')
      );
    });

    it('should fetch from repository if not cached', async () => {
      const mockGame = { id: 1, code: 'game1', name: 'Game 1' };
      mockCacheService.get.mockResolvedValue(null);
      mockGameRepository.findByCode.mockResolvedValue(mockGame as any);

      const result = await gameService.getGameDetail('game1');

      expect(result).toEqual(mockGame);
      expect(mockGameRepository.findByCode).toHaveBeenCalledWith('game1');
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('games:detail:game1'),
        mockGame,
        3600
      );
    });
  });

  describe('openGame', () => {
    it('should open game successfully and queue jobs', async () => {
      const gameData = {
        code: 'game1',
        fid: 'test-fid',
        uid: 'user123',
        metadata: { source: 'web' },
      };

      const mockGame = { id: 1, code: 'game1', name: 'Game 1' };
      mockGameRepository.findByCode.mockResolvedValue(mockGame as any);

      const result = await gameService.openGame(gameData);

      expect(result).toEqual({
        success: true,
        message: 'Game opened successfully',
      });

      expect(mockGameRepository.findByCode).toHaveBeenCalledWith('game1');
      expect(mockQueueService.addGameReportJob).toHaveBeenCalledWith({
        fid: 'test-fid',
        code: 'game1',
        uid: 'user123',
        month: expect.any(Number),
        year: expect.any(Number),
      });
      expect(mockQueueService.addLogEventJob).toHaveBeenCalledWith({
        fid: 'test-fid',
        uid: 'user123',
        eventType: 'game_open',
        eventData: {
          gameCode: 'game1',
          metadata: { source: 'web' },
        },
      });
    });

    it('should throw error if game not found', async () => {
      const gameData = {
        code: 'nonexistent',
        fid: 'test-fid',
      };

      mockGameRepository.findByCode.mockResolvedValue(null);

      await expect(gameService.openGame(gameData)).rejects.toThrow(NotFoundError);
      expect(mockQueueService.addGameReportJob).not.toHaveBeenCalled();
    });
  });

  describe('bulkGameOperation', () => {
    it('should perform bulk activate operation', async () => {
      const codes = ['game1', 'game2', 'game3'];
      mockGameRepository.updateStatus = vi.fn().mockResolvedValue(true);

      const result = await gameService.bulkGameOperation('activate', codes);

      expect(result).toEqual({
        successful: codes,
        failed: [],
        total: 3,
      });

      expect(mockGameRepository.updateStatus).toHaveBeenCalledTimes(3);
      codes.forEach(code => {
        expect(mockGameRepository.updateStatus).toHaveBeenCalledWith(code, 1);
      });
      expect(mockCacheService.invalidatePattern).toHaveBeenCalledWith('games:*');
    });

    it('should handle partial failures in bulk operation', async () => {
      const codes = ['game1', 'game2', 'game3'];
      mockGameRepository.updateStatus = vi.fn()
        .mockResolvedValueOnce(true)
        .mockRejectedValueOnce(new Error('Database error'))
        .mockResolvedValueOnce(true);

      const result = await gameService.bulkGameOperation('activate', codes);

      expect(result).toEqual({
        successful: ['game1', 'game3'],
        failed: [{ item: 'game2', error: 'Database error' }],
        total: 3,
      });
    });

    it('should throw error for unknown operation', async () => {
      const codes = ['game1'];
      mockGameRepository.updateStatus = vi.fn().mockImplementation(() => {
        throw new Error('Unknown operation: invalid');
      });

      const result = await gameService.bulkGameOperation('invalid', codes);

      expect(result.failed).toHaveLength(1);
      expect(result.failed[0].error).toContain('Unknown operation');
    });
  });

  describe('error handling', () => {
    it('should handle cache errors gracefully', async () => {
      mockCacheService.get.mockRejectedValue(new Error('Cache error'));
      mockGameRepository.findMany.mockResolvedValue([]);

      const result = await gameService.getListGames({});

      expect(result).toEqual([]);
      expect(mockGameRepository.findMany).toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      mockCacheService.get.mockResolvedValue(null);
      mockGameRepository.findMany.mockRejectedValue(new Error('Database error'));

      await expect(gameService.getListGames({})).rejects.toThrow('Database error');
    });
  });
});
