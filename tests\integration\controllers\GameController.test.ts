import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { Express } from 'express';
import { createTestApp } from '../../helpers/testApp';
import { createTestDatabase, cleanupTestDatabase } from '../../helpers/testDatabase';
import { createTestUser, generateTestToken } from '../../helpers/testAuth';
import { DrizzleDB } from '../../../src/types/database';

describe('GameController Integration Tests', () => {
  let app: Express;
  let db: DrizzleDB;
  let authToken: string;
  let testUserId: number;

  beforeAll(async () => {
    // Setup test database
    db = await createTestDatabase();
    
    // Create test app
    app = createTestApp(db);
    
    // Create test user and get auth token
    const testUser = await createTestUser(db, {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      roles: ['user'],
      permissions: ['games:read', 'games:write'],
    });
    
    testUserId = testUser.id;
    authToken = generateTestToken(testUser);
  });

  afterAll(async () => {
    await cleanupTestDatabase(db);
  });

  beforeEach(async () => {
    // Clean up test data before each test
    await db.delete(games).execute();
    await db.delete(fusGames).execute();
  });

  describe('GET /api/v1/game', () => {
    it('should return list of games', async () => {
      // Seed test data
      await db.insert(games).values([
        { code: 'game1', name: 'Test Game 1', status: 1 },
        { code: 'game2', name: 'Test Game 2', status: 1 },
        { code: 'game3', name: 'Test Game 3', status: 0 },
      ]);

      const response = await request(app)
        .get('/api/v1/game')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        code: 200,
        message: 'Games retrieved successfully',
        metadata: expect.any(Array),
      });

      expect(response.body.metadata).toHaveLength(3);
      expect(response.body.metadata[0]).toMatchObject({
        code: expect.any(String),
        name: expect.any(String),
        status: expect.any(Number),
      });
    });

    it('should filter games by status', async () => {
      // Seed test data
      await db.insert(games).values([
        { code: 'game1', name: 'Active Game', status: 1 },
        { code: 'game2', name: 'Inactive Game', status: 0 },
      ]);

      const response = await request(app)
        .get('/api/v1/game?status=1')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.metadata).toHaveLength(1);
      expect(response.body.metadata[0].status).toBe(1);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/v1/game')
        .expect(401);
    });

    it('should require proper permissions', async () => {
      const limitedUser = await createTestUser(db, {
        username: 'limiteduser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        roles: ['user'],
        permissions: [], // No permissions
      });

      const limitedToken = generateTestToken(limitedUser);

      await request(app)
        .get('/api/v1/game')
        .set('Authorization', `Bearer ${limitedToken}`)
        .expect(403);
    });
  });

  describe('POST /api/v1/game', () => {
    it('should create new games successfully', async () => {
      const gameData = {
        fid: 'test-fid',
        data: [
          { code: 'new-game-1', name: 'New Game 1' },
          { code: 'new-game-2', name: 'New Game 2' },
        ],
      };

      const response = await request(app)
        .post('/api/v1/game')
        .set('Authorization', `Bearer ${authToken}`)
        .send(gameData)
        .expect(201);

      expect(response.body).toMatchObject({
        status: 'success',
        code: 201,
        message: 'Game created successfully',
        metadata: expect.objectContaining({
          fid: 'test-fid',
          data: gameData.data,
        }),
      });

      // Verify games were created in database
      const createdGames = await db.select().from(games).where(
        or(
          eq(games.code, 'new-game-1'),
          eq(games.code, 'new-game-2')
        )
      );

      expect(createdGames).toHaveLength(2);
    });

    it('should validate request data', async () => {
      const invalidData = {
        fid: '', // Invalid empty FID
        data: [], // Empty data array
      };

      const response = await request(app)
        .post('/api/v1/game')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        code: 400,
        message: 'Validation failed',
        errors: expect.any(Object),
      });
    });

    it('should prevent duplicate game codes', async () => {
      // Create existing game
      await db.insert(games).values({
        code: 'existing-game',
        name: 'Existing Game',
        status: 1,
      });

      const gameData = {
        fid: 'test-fid',
        data: [
          { code: 'existing-game', name: 'Duplicate Game' },
        ],
      };

      const response = await request(app)
        .post('/api/v1/game')
        .set('Authorization', `Bearer ${authToken}`)
        .send(gameData)
        .expect(400);

      expect(response.body.message).toContain('already exist');
    });
  });

  describe('GET /api/v1/game/:code', () => {
    it('should return game detail', async () => {
      // Seed test data
      await db.insert(games).values({
        code: 'test-game',
        name: 'Test Game Detail',
        logo: 'https://example.com/logo.png',
        status: 1,
      });

      const response = await request(app)
        .get('/api/v1/game/test-game')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        code: 200,
        message: 'Game detail retrieved successfully',
        metadata: expect.objectContaining({
          code: 'test-game',
          name: 'Test Game Detail',
          logo: 'https://example.com/logo.png',
          status: 1,
        }),
      });
    });

    it('should return 404 for non-existent game', async () => {
      const response = await request(app)
        .get('/api/v1/game/non-existent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toMatchObject({
        status: 'error',
        code: 404,
        message: 'Game not found',
      });
    });
  });

  describe('POST /api/v1/game/open', () => {
    it('should open game successfully', async () => {
      // Seed test data
      await db.insert(games).values({
        code: 'playable-game',
        name: 'Playable Game',
        status: 1,
      });

      const openData = {
        code: 'playable-game',
        fid: 'test-fid',
        uid: 'user123',
        metadata: { source: 'web' },
      };

      const response = await request(app)
        .post('/api/v1/game/open')
        .set('Authorization', `Bearer ${authToken}`)
        .send(openData)
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'success',
        code: 200,
        message: 'Game opened successfully',
        metadata: expect.objectContaining({
          success: true,
          message: 'Game opened successfully',
        }),
      });
    });

    it('should validate open game request', async () => {
      const invalidData = {
        code: '', // Empty code
        fid: 'test-fid',
      };

      const response = await request(app)
        .post('/api/v1/game/open')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        code: 400,
        message: 'Validation failed',
      });
    });

    it('should return 404 for non-existent game', async () => {
      const openData = {
        code: 'non-existent-game',
        fid: 'test-fid',
      };

      const response = await request(app)
        .post('/api/v1/game/open')
        .set('Authorization', `Bearer ${authToken}`)
        .send(openData)
        .expect(404);

      expect(response.body.message).toContain('not found');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      // Make multiple requests quickly
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .get('/api/v1/game')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);

      // Some requests should succeed, others should be rate limited
      const successfulRequests = responses.filter(r => r.status === 200);
      const rateLimitedRequests = responses.filter(r => r.status === 429);

      expect(successfulRequests.length).toBeGreaterThan(0);
      expect(rateLimitedRequests.length).toBeGreaterThan(0);

      // Check rate limit headers
      const rateLimitedResponse = rateLimitedRequests[0];
      expect(rateLimitedResponse.headers).toHaveProperty('x-ratelimit-limit');
      expect(rateLimitedResponse.headers).toHaveProperty('x-ratelimit-remaining');
      expect(rateLimitedResponse.headers).toHaveProperty('retry-after');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Simulate database error by using invalid query
      // This would typically be done by mocking the database connection
      // For integration tests, we'll test with malformed requests instead

      const response = await request(app)
        .get('/api/v1/game')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toMatchObject({
        status: 'error',
        code: 401,
        message: expect.any(String),
      });
    });

    it('should return proper error format', async () => {
      const response = await request(app)
        .post('/api/v1/game')
        .set('Authorization', `Bearer ${authToken}`)
        .send({}) // Empty body
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('errors');
    });
  });
});
