import { APIError } from './base.error';

export class BadRequestError extends APIError {
  public readonly statusCode = 400;
  public readonly isOperational = true;

  constructor(message: string = 'Bad request', errors?: Record<string, string>) {
    super(message, errors);
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, BadRequestError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
      errors: this.errors,
    };
  }
}
