import * as dotenv from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = z.object({
  // Application Settings
  NODE_ENV: z.enum(['development', 'production', 'test', 'staging']).default('development'),
  PORT: z.coerce.number().default(3000),
  APP_NAME: z.string().default('FUS-CMS'),
  APP_VERSION: z.string().default('2.0.0'),
  APP_URL: z.string().url().default('http://localhost:3000'),

  // Database Configuration
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.coerce.number().default(3306),
  DB_USER: z.string().min(1, 'Database user is required'),
  DB_PASSWORD: z.string().min(1, 'Database password is required'),
  DB_NAME: z.string().min(1, 'Database name is required'),
  DB_SSL: z.coerce.boolean().default(false),
  DB_TIMEZONE: z.string().default('+07:00'),
  
  // Database Pool Settings
  DB_POOL_MIN: z.coerce.number().default(2),
  DB_POOL_MAX: z.coerce.number().default(10),
  DB_POOL_ACQUIRE: z.coerce.number().default(30000),
  DB_POOL_IDLE: z.coerce.number().default(10000),

  // Log Database
  LOG_DB_HOST: z.string().default('localhost'),
  LOG_DB_PORT: z.coerce.number().default(3306),
  LOG_DB_USER: z.string().min(1, 'Log database user is required'),
  LOG_DB_PASSWORD: z.string().min(1, 'Log database password is required'),
  LOG_DB_NAME: z.string().min(1, 'Log database name is required'),
  LOG_DB_SSL: z.coerce.boolean().default(false),

  // Database URLs (optional, overrides individual settings)
  DATABASE_URL: z.string().optional(),
  LOG_DATABASE_URL: z.string().optional(),

  // Redis Configuration
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.coerce.number().default(6379),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.coerce.number().default(0),
  REDIS_KEY_PREFIX: z.string().default('fus-cms:'),
  REDIS_CONNECT_TIMEOUT: z.coerce.number().default(10000),
  REDIS_COMMAND_TIMEOUT: z.coerce.number().default(5000),
  REDIS_RETRY_DELAY_ON_FAILOVER: z.coerce.number().default(100),
  REDIS_MAX_RETRIES_PER_REQUEST: z.coerce.number().default(3),
  REDIS_URL: z.string().optional(),

  // JWT Authentication
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('24h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  JWT_ISSUER: z.string().default('fus-cms'),
  JWT_AUDIENCE: z.string().default('fus-cms-api'),
  JWT_ALGORITHM: z.string().default('HS256'),

  // Security Settings
  PASSWORD_MIN_LENGTH: z.coerce.number().default(8),
  PASSWORD_MAX_LENGTH: z.coerce.number().default(128),
  PASSWORD_REQUIRE_UPPERCASE: z.coerce.boolean().default(true),
  PASSWORD_REQUIRE_LOWERCASE: z.coerce.boolean().default(true),
  PASSWORD_REQUIRE_NUMBERS: z.coerce.boolean().default(true),
  PASSWORD_REQUIRE_SYMBOLS: z.coerce.boolean().default(true),
  PASSWORD_HISTORY_LIMIT: z.coerce.number().default(5),

  // API Security
  API_SECRET_KEY: z.string().min(32, 'API secret key must be at least 32 characters'),
  WEBHOOK_SECRET: z.string().min(16, 'Webhook secret must be at least 16 characters'),
  SIGNATURE_ALGORITHM: z.string().default('sha256'),
  SIGNATURE_TIMESTAMP_TOLERANCE: z.coerce.number().default(300),

  // CORS Settings
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  CORS_CREDENTIALS: z.coerce.boolean().default(true),
  CORS_METHODS: z.string().default('GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS'),
  CORS_ALLOWED_HEADERS: z.string().default('Content-Type,Authorization,X-Requested-With,X-API-Key,X-Signature,X-Timestamp'),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.coerce.number().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(1000),
  RATE_LIMIT_SKIP_SUCCESSFUL: z.coerce.boolean().default(false),
  RATE_LIMIT_SKIP_FAILED: z.coerce.boolean().default(false),

  AUTH_RATE_LIMIT_WINDOW_MS: z.coerce.number().default(900000),
  AUTH_RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(5),
  AUTH_RATE_LIMIT_SKIP_SUCCESSFUL: z.coerce.boolean().default(true),

  API_RATE_LIMIT_WINDOW_MS: z.coerce.number().default(60000),
  API_RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(100),

  UPLOAD_RATE_LIMIT_WINDOW_MS: z.coerce.number().default(3600000),
  UPLOAD_RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(10),

  PASSWORD_RESET_RATE_LIMIT_WINDOW_MS: z.coerce.number().default(3600000),
  PASSWORD_RESET_RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(3),

  // Cache Settings
  CACHE_TTL_DEFAULT: z.coerce.number().default(3600),
  CACHE_TTL_SHORT: z.coerce.number().default(900),
  CACHE_TTL_LONG: z.coerce.number().default(7200),
  CACHE_TTL_VERY_LONG: z.coerce.number().default(86400),

  CACHE_TTL_GAMES_LIST: z.coerce.number().default(1800),
  CACHE_TTL_GAMES_DETAIL: z.coerce.number().default(3600),
  CACHE_TTL_BANNERS_LIST: z.coerce.number().default(1800),
  CACHE_TTL_USER_SESSION: z.coerce.number().default(900),
  CACHE_TTL_ANALYTICS: z.coerce.number().default(1800),

  // Queue Configuration
  QUEUE_REDIS_HOST: z.string().default('localhost'),
  QUEUE_REDIS_PORT: z.coerce.number().default(6379),
  QUEUE_REDIS_PASSWORD: z.string().optional(),
  QUEUE_REDIS_DB: z.coerce.number().default(1),
  QUEUE_REDIS_KEY_PREFIX: z.string().default('fus-cms:bull:'),

  QUEUE_DEFAULT_JOB_ATTEMPTS: z.coerce.number().default(3),
  QUEUE_DEFAULT_JOB_BACKOFF_TYPE: z.string().default('exponential'),
  QUEUE_DEFAULT_JOB_BACKOFF_DELAY: z.coerce.number().default(2000),
  QUEUE_DEFAULT_REMOVE_ON_COMPLETE: z.coerce.number().default(10),
  QUEUE_DEFAULT_REMOVE_ON_FAIL: z.coerce.number().default(5),

  QUEUE_WORKER_CONCURRENCY: z.coerce.number().default(5),
  QUEUE_WORKER_MAX_STALLED_COUNT: z.coerce.number().default(1),
  QUEUE_WORKER_STALLED_INTERVAL: z.coerce.number().default(30000),

  // Logging Configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FORMAT: z.enum(['json', 'simple']).default('json'),
  LOG_DATE_PATTERN: z.string().default('YYYY-MM-DD-HH'),
  LOG_MAX_SIZE: z.string().default('20m'),
  LOG_MAX_FILES: z.string().default('14d'),

  LOG_FILE_ERROR: z.string().default('logs/error.log'),
  LOG_FILE_COMBINED: z.string().default('logs/combined.log'),
  LOG_FILE_ACCESS: z.string().default('logs/access.log'),

  LOG_EXTERNAL_ENABLED: z.coerce.boolean().default(false),
  LOG_EXTERNAL_URL: z.string().optional(),
  LOG_EXTERNAL_API_KEY: z.string().optional(),

  // File Upload Settings
  UPLOAD_MAX_FILE_SIZE: z.coerce.number().default(10485760), // 10MB
  UPLOAD_ALLOWED_TYPES: z.string().default('image/jpeg,image/png,image/gif,image/webp,video/mp4,application/pdf'),
  UPLOAD_DESTINATION: z.string().default('uploads'),
  UPLOAD_TEMP_DIR: z.string().default('temp'),
  UPLOAD_PUBLIC_URL: z.string().default('http://localhost:3000/uploads'),

  // Cloud Storage
  CLOUD_STORAGE_ENABLED: z.coerce.boolean().default(false),
  CLOUD_STORAGE_PROVIDER: z.string().default('aws'),
  CLOUD_STORAGE_BUCKET: z.string().optional(),
  CLOUD_STORAGE_REGION: z.string().default('us-east-1'),
  CLOUD_STORAGE_ACCESS_KEY: z.string().optional(),
  CLOUD_STORAGE_SECRET_KEY: z.string().optional(),
  CLOUD_STORAGE_CDN_URL: z.string().optional(),

  // Email Configuration
  EMAIL_ENABLED: z.coerce.boolean().default(false),
  EMAIL_PROVIDER: z.string().default('smtp'),
  EMAIL_HOST: z.string().optional(),
  EMAIL_PORT: z.coerce.number().optional(),
  EMAIL_SECURE: z.coerce.boolean().default(false),
  EMAIL_USER: z.string().optional(),
  EMAIL_PASSWORD: z.string().optional(),
  EMAIL_FROM_NAME: z.string().default('FUS CMS'),
  EMAIL_FROM_ADDRESS: z.string().optional(),

  // Monitoring & Health Check
  HEALTH_CHECK_ENABLED: z.coerce.boolean().default(true),
  HEALTH_CHECK_ENDPOINT: z.string().default('/health-check'),
  HEALTH_CHECK_DATABASE: z.coerce.boolean().default(true),
  HEALTH_CHECK_REDIS: z.coerce.boolean().default(true),
  HEALTH_CHECK_QUEUES: z.coerce.boolean().default(true),

  METRICS_ENABLED: z.coerce.boolean().default(false),
  METRICS_ENDPOINT: z.string().default('/metrics'),
  METRICS_COLLECT_DEFAULT: z.coerce.boolean().default(true),
  METRICS_COLLECT_HTTP: z.coerce.boolean().default(true),
  METRICS_COLLECT_SYSTEM: z.coerce.boolean().default(true),

  // Development Settings
  DEBUG_ENABLED: z.coerce.boolean().default(false),
  DEBUG_SQL_QUERIES: z.coerce.boolean().default(false),
  DEBUG_CACHE_OPERATIONS: z.coerce.boolean().default(false),
  DEBUG_QUEUE_JOBS: z.coerce.boolean().default(false),
  DEBUG_API_REQUESTS: z.coerce.boolean().default(false),

  DEV_TOOLS_ENABLED: z.coerce.boolean().default(false),
  API_DOCS_ENABLED: z.coerce.boolean().default(false),
  API_DOCS_ENDPOINT: z.string().default('/api/docs'),
  QUEUE_DASHBOARD_ENABLED: z.coerce.boolean().default(false),
  QUEUE_DASHBOARD_ENDPOINT: z.string().default('/admin/queues'),

  // Feature Flags
  FEATURE_ANALYTICS_ENABLED: z.coerce.boolean().default(true),
  FEATURE_REAL_TIME_TRACKING: z.coerce.boolean().default(true),
  FEATURE_ADVANCED_CACHING: z.coerce.boolean().default(true),
  FEATURE_QUEUE_DASHBOARD: z.coerce.boolean().default(true),
  FEATURE_API_VERSIONING: z.coerce.boolean().default(true),
  FEATURE_REQUEST_SIGNING: z.coerce.boolean().default(false),
  FEATURE_RATE_LIMITING: z.coerce.boolean().default(true),
  FEATURE_AUDIT_LOGGING: z.coerce.boolean().default(true),
});

// Validate and parse environment variables
let env: z.infer<typeof envSchema>;

try {
  env = envSchema.parse(process.env);
} catch (error) {
  console.error('❌ Invalid environment configuration:');
  if (error instanceof z.ZodError) {
    error.errors.forEach((err) => {
      console.error(`  - ${err.path.join('.')}: ${err.message}`);
    });
  }
  process.exit(1);
}

// Export validated environment configuration
export const config = {
  // Application
  app: {
    name: env.APP_NAME,
    version: env.APP_VERSION,
    url: env.APP_URL,
    env: env.NODE_ENV,
    port: env.PORT,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
  },

  // Database
  database: {
    host: env.DB_HOST,
    port: env.DB_PORT,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    name: env.DB_NAME,
    ssl: env.DB_SSL,
    timezone: env.DB_TIMEZONE,
    url: env.DATABASE_URL,
    pool: {
      min: env.DB_POOL_MIN,
      max: env.DB_POOL_MAX,
      acquire: env.DB_POOL_ACQUIRE,
      idle: env.DB_POOL_IDLE,
    },
  },

  // Log Database
  logDatabase: {
    host: env.LOG_DB_HOST,
    port: env.LOG_DB_PORT,
    user: env.LOG_DB_USER,
    password: env.LOG_DB_PASSWORD,
    name: env.LOG_DB_NAME,
    ssl: env.LOG_DB_SSL,
    url: env.LOG_DATABASE_URL,
  },

  // Redis
  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
    keyPrefix: env.REDIS_KEY_PREFIX,
    connectTimeout: env.REDIS_CONNECT_TIMEOUT,
    commandTimeout: env.REDIS_COMMAND_TIMEOUT,
    retryDelayOnFailover: env.REDIS_RETRY_DELAY_ON_FAILOVER,
    maxRetriesPerRequest: env.REDIS_MAX_RETRIES_PER_REQUEST,
    url: env.REDIS_URL,
  },

  // JWT
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
    issuer: env.JWT_ISSUER,
    audience: env.JWT_AUDIENCE,
    algorithm: env.JWT_ALGORITHM,
  },

  // Security
  security: {
    password: {
      minLength: env.PASSWORD_MIN_LENGTH,
      maxLength: env.PASSWORD_MAX_LENGTH,
      requireUppercase: env.PASSWORD_REQUIRE_UPPERCASE,
      requireLowercase: env.PASSWORD_REQUIRE_LOWERCASE,
      requireNumbers: env.PASSWORD_REQUIRE_NUMBERS,
      requireSymbols: env.PASSWORD_REQUIRE_SYMBOLS,
      historyLimit: env.PASSWORD_HISTORY_LIMIT,
    },
    api: {
      secretKey: env.API_SECRET_KEY,
      webhookSecret: env.WEBHOOK_SECRET,
      signatureAlgorithm: env.SIGNATURE_ALGORITHM,
      timestampTolerance: env.SIGNATURE_TIMESTAMP_TOLERANCE,
    },
    cors: {
      origin: env.CORS_ORIGIN.split(','),
      credentials: env.CORS_CREDENTIALS,
      methods: env.CORS_METHODS.split(','),
      allowedHeaders: env.CORS_ALLOWED_HEADERS.split(','),
    },
  },

  // Rate Limiting
  rateLimit: {
    global: {
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
      skipSuccessful: env.RATE_LIMIT_SKIP_SUCCESSFUL,
      skipFailed: env.RATE_LIMIT_SKIP_FAILED,
    },
    auth: {
      windowMs: env.AUTH_RATE_LIMIT_WINDOW_MS,
      maxRequests: env.AUTH_RATE_LIMIT_MAX_REQUESTS,
      skipSuccessful: env.AUTH_RATE_LIMIT_SKIP_SUCCESSFUL,
    },
    api: {
      windowMs: env.API_RATE_LIMIT_WINDOW_MS,
      maxRequests: env.API_RATE_LIMIT_MAX_REQUESTS,
    },
    upload: {
      windowMs: env.UPLOAD_RATE_LIMIT_WINDOW_MS,
      maxRequests: env.UPLOAD_RATE_LIMIT_MAX_REQUESTS,
    },
    passwordReset: {
      windowMs: env.PASSWORD_RESET_RATE_LIMIT_WINDOW_MS,
      maxRequests: env.PASSWORD_RESET_RATE_LIMIT_MAX_REQUESTS,
    },
  },

  // Cache
  cache: {
    ttl: {
      default: env.CACHE_TTL_DEFAULT,
      short: env.CACHE_TTL_SHORT,
      long: env.CACHE_TTL_LONG,
      veryLong: env.CACHE_TTL_VERY_LONG,
      gamesList: env.CACHE_TTL_GAMES_LIST,
      gamesDetail: env.CACHE_TTL_GAMES_DETAIL,
      bannersList: env.CACHE_TTL_BANNERS_LIST,
      userSession: env.CACHE_TTL_USER_SESSION,
      analytics: env.CACHE_TTL_ANALYTICS,
    },
  },

  // Queue
  queue: {
    redis: {
      host: env.QUEUE_REDIS_HOST,
      port: env.QUEUE_REDIS_PORT,
      password: env.QUEUE_REDIS_PASSWORD,
      db: env.QUEUE_REDIS_DB,
      keyPrefix: env.QUEUE_REDIS_KEY_PREFIX,
    },
    defaultJob: {
      attempts: env.QUEUE_DEFAULT_JOB_ATTEMPTS,
      backoffType: env.QUEUE_DEFAULT_JOB_BACKOFF_TYPE,
      backoffDelay: env.QUEUE_DEFAULT_JOB_BACKOFF_DELAY,
      removeOnComplete: env.QUEUE_DEFAULT_REMOVE_ON_COMPLETE,
      removeOnFail: env.QUEUE_DEFAULT_REMOVE_ON_FAIL,
    },
    worker: {
      concurrency: env.QUEUE_WORKER_CONCURRENCY,
      maxStalledCount: env.QUEUE_WORKER_MAX_STALLED_COUNT,
      stalledInterval: env.QUEUE_WORKER_STALLED_INTERVAL,
    },
  },

  // Logging
  logging: {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT,
    datePattern: env.LOG_DATE_PATTERN,
    maxSize: env.LOG_MAX_SIZE,
    maxFiles: env.LOG_MAX_FILES,
    files: {
      error: env.LOG_FILE_ERROR,
      combined: env.LOG_FILE_COMBINED,
      access: env.LOG_FILE_ACCESS,
    },
    external: {
      enabled: env.LOG_EXTERNAL_ENABLED,
      url: env.LOG_EXTERNAL_URL,
      apiKey: env.LOG_EXTERNAL_API_KEY,
    },
  },

  // Upload
  upload: {
    maxFileSize: env.UPLOAD_MAX_FILE_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(','),
    destination: env.UPLOAD_DESTINATION,
    tempDir: env.UPLOAD_TEMP_DIR,
    publicUrl: env.UPLOAD_PUBLIC_URL,
    cloudStorage: {
      enabled: env.CLOUD_STORAGE_ENABLED,
      provider: env.CLOUD_STORAGE_PROVIDER,
      bucket: env.CLOUD_STORAGE_BUCKET,
      region: env.CLOUD_STORAGE_REGION,
      accessKey: env.CLOUD_STORAGE_ACCESS_KEY,
      secretKey: env.CLOUD_STORAGE_SECRET_KEY,
      cdnUrl: env.CLOUD_STORAGE_CDN_URL,
    },
  },

  // Email
  email: {
    enabled: env.EMAIL_ENABLED,
    provider: env.EMAIL_PROVIDER,
    host: env.EMAIL_HOST,
    port: env.EMAIL_PORT,
    secure: env.EMAIL_SECURE,
    user: env.EMAIL_USER,
    password: env.EMAIL_PASSWORD,
    from: {
      name: env.EMAIL_FROM_NAME,
      address: env.EMAIL_FROM_ADDRESS,
    },
  },

  // Monitoring
  monitoring: {
    healthCheck: {
      enabled: env.HEALTH_CHECK_ENABLED,
      endpoint: env.HEALTH_CHECK_ENDPOINT,
      database: env.HEALTH_CHECK_DATABASE,
      redis: env.HEALTH_CHECK_REDIS,
      queues: env.HEALTH_CHECK_QUEUES,
    },
    metrics: {
      enabled: env.METRICS_ENABLED,
      endpoint: env.METRICS_ENDPOINT,
      collectDefault: env.METRICS_COLLECT_DEFAULT,
      collectHttp: env.METRICS_COLLECT_HTTP,
      collectSystem: env.METRICS_COLLECT_SYSTEM,
    },
  },

  // Development
  development: {
    debug: {
      enabled: env.DEBUG_ENABLED,
      sqlQueries: env.DEBUG_SQL_QUERIES,
      cacheOperations: env.DEBUG_CACHE_OPERATIONS,
      queueJobs: env.DEBUG_QUEUE_JOBS,
      apiRequests: env.DEBUG_API_REQUESTS,
    },
    tools: {
      enabled: env.DEV_TOOLS_ENABLED,
      apiDocs: {
        enabled: env.API_DOCS_ENABLED,
        endpoint: env.API_DOCS_ENDPOINT,
      },
      queueDashboard: {
        enabled: env.QUEUE_DASHBOARD_ENABLED,
        endpoint: env.QUEUE_DASHBOARD_ENDPOINT,
      },
    },
  },

  // Features
  features: {
    analyticsEnabled: env.FEATURE_ANALYTICS_ENABLED,
    realTimeTracking: env.FEATURE_REAL_TIME_TRACKING,
    advancedCaching: env.FEATURE_ADVANCED_CACHING,
    queueDashboard: env.FEATURE_QUEUE_DASHBOARD,
    apiVersioning: env.FEATURE_API_VERSIONING,
    requestSigning: env.FEATURE_REQUEST_SIGNING,
    rateLimiting: env.FEATURE_RATE_LIMITING,
    auditLogging: env.FEATURE_AUDIT_LOGGING,
  },
} as const;

// Export individual configurations for convenience
export const {
  app,
  database,
  logDatabase,
  redis,
  jwt,
  security,
  rateLimit,
  cache,
  queue,
  logging,
  upload,
  email,
  monitoring,
  development,
  features,
} = config;

// Validate critical configurations
if (config.app.isProduction) {
  // Production-specific validations
  if (config.jwt.secret.length < 64) {
    console.warn('⚠️  JWT secret should be at least 64 characters in production');
  }
  
  if (!config.database.ssl && !config.database.url?.includes('localhost')) {
    console.warn('⚠️  Consider enabling SSL for database connections in production');
  }
  
  if (config.development.debug.enabled) {
    console.warn('⚠️  Debug mode should be disabled in production');
  }
}

console.log(`✅ Environment configuration loaded for ${config.app.env} environment`);
