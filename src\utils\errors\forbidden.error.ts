import { APIError } from './base.error';

export class ForbiddenError extends APIError {
  public readonly statusCode = 403;
  public readonly isOperational = true;

  constructor(message: string = 'Forbidden') {
    super(message);
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, ForbiddenError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
    };
  }
}
