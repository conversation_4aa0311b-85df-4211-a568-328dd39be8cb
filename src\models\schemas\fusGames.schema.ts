import { mysqlTable, int, varchar, json, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const fusGames = mysqlTable('fus_games', {
  id: int('id').primaryKey().autoincrement(),
  fid: varchar('fid', { length: 255 }).notNull(),
  data: json('data').notNull(),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  fidIdx: index('idx_fus_games_fid').on(table.fid),
}));

export type FusGame = typeof fusGames.$inferSelect;
export type NewFusGame = typeof fusGames.$inferInsert;
