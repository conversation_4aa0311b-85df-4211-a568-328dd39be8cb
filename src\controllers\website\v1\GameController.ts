import { Response, NextFunction } from 'express';
import { BaseController } from '../../base/BaseController';
import { GameService } from '../../../services/website/GameService';
import { APIRequest } from '../../../types/api';
import {
  CreateGameRequest,
  GetGamesListRequest,
  GetTopGamesRequest,
  GetUserGamesRequest,
  GetGameDetailRequest,
  OpenGameRequest,
  RssRequest,
} from '../../../validators/website/game.validator';

export class GameController extends BaseController {
  private gameService: GameService;

  constructor(gameService: GameService) {
    super(gameService);
    this.gameService = gameService;
  }

  // GET /game - Get list of games
  getListGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListGames');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const { query } = req as GetGamesListRequest;
        return await this.gameService.getListGames(query);
      },
      'Games retrieved successfully'
    );
  });

  // POST /game - Create new game
  createGame = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'createGame');

    await this.handleCreateRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as CreateGameRequest;
        return await this.gameService.createGame(body);
      },
      'Game created successfully'
    );
  });

  // GET /game/top/:fid - Get top games by FID
  getListTopGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListTopGames');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const { params, query } = req as GetTopGamesRequest;
        return await this.gameService.getListTopGames(params.fid, query);
      },
      'Top games retrieved successfully'
    );
  });

  // GET /game/top/:fid/:uid - Get user games
  getListUserGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListUserGames');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const { params, query } = req as GetUserGamesRequest;
        return await this.gameService.getListUserGames(params.fid, params.uid, query);
      },
      'User games retrieved successfully'
    );
  });

  // GET /game/:code - Get game detail
  getGameDetail = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getGameDetail');

    await this.handleDetailRequest(
      req,
      res,
      next,
      async (req) => {
        const { params } = req as GetGameDetailRequest;
        return await this.gameService.getGameDetail(params.code);
      },
      'Game detail retrieved successfully',
      'Game not found'
    );
  });

  // POST /game/open - Open game
  openGame = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'openGame');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as OpenGameRequest;
        return await this.gameService.openGame(body);
      },
      'Game opened successfully'
    );
  });

  // GET /rss - Get RSS data
  getListRss = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListRss');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { query } = req as RssRequest;
        return await this.gameService.getListRss(query.link);
      },
      'RSS data retrieved successfully'
    );
  });

  // GET/POST /update-rss - Update RSS feeds
  updateRss = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'updateRss');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        return await this.gameService.updateRss();
      },
      'RSS feeds updated successfully'
    );
  });

  // Game statistics endpoint
  getGameStats = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getGameStats');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'codes', 'period', 'groupBy']);
        const pagination = this.getPaginationParams(req);
        return await this.gameService.getGameStats(filters, pagination);
      },
      'Game statistics retrieved successfully'
    );
  });

  // Bulk game operations
  bulkGameOperation = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'bulkGameOperation');

    await this.handleBulkRequest(
      req,
      res,
      next,
      async (req) => {
        const { operation, codes, reason } = req.body;
        return await this.gameService.bulkGameOperation(operation, codes, reason);
      },
      'Bulk game operation completed'
    );
  });

  // Search games
  searchGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'searchGames');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const { q, ...filters } = req.query;
        const pagination = this.getPaginationParams(req);
        const sort = this.getSortParams(req, ['name', 'code', 'created_at', 'popularity'], 'name');
        
        return await this.gameService.searchGames(q as string, filters, pagination, sort);
      },
      'Game search results retrieved successfully'
    );
  });

  // Get game categories
  getGameCategories = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getGameCategories');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['status']);
        return await this.gameService.getGameCategories(filters);
      },
      'Game categories retrieved successfully'
    );
  });

  // Get trending games
  getTrendingGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getTrendingGames');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const { limit = 10, period = 'week' } = req.query;
        return await this.gameService.getTrendingGames(Number(limit), period as string);
      },
      'Trending games retrieved successfully'
    );
  });

  // Get game analytics
  getGameAnalytics = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getGameAnalytics');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'codes', 'period', 'metrics', 'groupBy']);
        const pagination = this.getPaginationParams(req);
        return await this.gameService.getGameAnalytics(filters, pagination);
      },
      'Game analytics retrieved successfully'
    );
  });
}
