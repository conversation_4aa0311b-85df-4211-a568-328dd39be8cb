import { Request, Response, NextFunction } from 'express';
import { APIRequest } from '../../types/api';
import { BaseService } from '../../services/base/BaseService';
import { SuccessResponse, ErrorResponse } from '../../utils/responses';
import { ValidationError } from '../../utils/errors/validation.error';
import { NotFoundError } from '../../utils/errors/notFound.error';
import { BadRequestError } from '../../utils/errors/badRequest.error';

export abstract class BaseController {
  protected readonly service: BaseService;

  constructor(service: BaseService) {
    this.service = service;
  }

  // Generic request handler with error handling
  protected async handleRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<T>,
    successMessage: string = 'Operation completed successfully'
  ): Promise<void> {
    try {
      const result = await handler(req);
      new SuccessResponse(result, successMessage).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle paginated requests
  protected async handlePaginatedRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<{
      items: T[];
      totalItems: number;
      itemsPerPage: number;
      currentPage: number;
      totalPages: number;
    }>,
    successMessage: string = 'Data retrieved successfully'
  ): Promise<void> {
    try {
      const result = await handler(req);
      new SuccessResponse(result, successMessage).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle list requests
  protected async handleListRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<T[]>,
    successMessage: string = 'List retrieved successfully'
  ): Promise<void> {
    try {
      const result = await handler(req);
      new SuccessResponse(result, successMessage).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle detail requests
  protected async handleDetailRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<T | null>,
    successMessage: string = 'Detail retrieved successfully',
    notFoundMessage: string = 'Resource not found'
  ): Promise<void> {
    try {
      const result = await handler(req);
      if (!result) {
        throw new NotFoundError(notFoundMessage);
      }
      new SuccessResponse(result, successMessage).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle create requests
  protected async handleCreateRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<T>,
    successMessage: string = 'Resource created successfully'
  ): Promise<void> {
    try {
      const result = await handler(req);
      new SuccessResponse(result, successMessage, 201).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle update requests
  protected async handleUpdateRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<T | null>,
    successMessage: string = 'Resource updated successfully',
    notFoundMessage: string = 'Resource not found'
  ): Promise<void> {
    try {
      const result = await handler(req);
      if (!result) {
        throw new NotFoundError(notFoundMessage);
      }
      new SuccessResponse(result, successMessage).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle delete requests
  protected async handleDeleteRequest(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<boolean>,
    successMessage: string = 'Resource deleted successfully',
    notFoundMessage: string = 'Resource not found'
  ): Promise<void> {
    try {
      const result = await handler(req);
      if (!result) {
        throw new NotFoundError(notFoundMessage);
      }
      new SuccessResponse(null, successMessage, 204).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Handle bulk operations
  protected async handleBulkRequest<T>(
    req: APIRequest,
    res: Response,
    next: NextFunction,
    handler: (req: APIRequest) => Promise<{
      successful: T[];
      failed: Array<{ item: any; error: string }>;
      total: number;
    }>,
    successMessage: string = 'Bulk operation completed'
  ): Promise<void> {
    try {
      const result = await handler(req);
      const message = `${successMessage}. ${result.successful.length}/${result.total} items processed successfully.`;
      new SuccessResponse(result, message).send(res);
    } catch (error) {
      next(error);
    }
  }

  // Validate required parameters
  protected validateRequiredParams(req: APIRequest, params: string[]): void {
    const missing = params.filter(param => {
      const value = req.params[param] || req.query[param] || req.body[param];
      return value === undefined || value === null || value === '';
    });

    if (missing.length > 0) {
      throw new BadRequestError(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  // Extract pagination parameters
  protected getPaginationParams(req: APIRequest): {
    page: number;
    limit: number;
    offset: number;
  } {
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  // Extract filter parameters
  protected getFilterParams(req: APIRequest, allowedFilters: string[]): Record<string, any> {
    const filters: Record<string, any> = {};

    allowedFilters.forEach(filter => {
      const value = req.query[filter];
      if (value !== undefined && value !== null && value !== '') {
        filters[filter] = value;
      }
    });

    return filters;
  }

  // Extract sort parameters
  protected getSortParams(req: APIRequest, allowedFields: string[], defaultField: string = 'id'): {
    field: string;
    direction: 'asc' | 'desc';
  } {
    const sortBy = req.query.sortBy as string || defaultField;
    const sortOrder = req.query.sortOrder as string || 'asc';

    const field = allowedFields.includes(sortBy) ? sortBy : defaultField;
    const direction = ['asc', 'desc'].includes(sortOrder) ? sortOrder as 'asc' | 'desc' : 'asc';

    return { field, direction };
  }

  // Log request for debugging
  protected logRequest(req: APIRequest, action: string): void {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[${new Date().toISOString()}] ${action}:`, {
        method: req.method,
        url: req.url,
        params: req.params,
        query: req.query,
        body: req.body,
        user: req.user?.id,
        ip: req.ip,
      });
    }
  }

  // Async wrapper for controller methods
  protected asyncHandler(fn: (req: APIRequest, res: Response, next: NextFunction) => Promise<void>) {
    return (req: APIRequest, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }
}
