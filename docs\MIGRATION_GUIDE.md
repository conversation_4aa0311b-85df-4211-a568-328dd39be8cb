# FUS CMS Migration Guide

## Overview

This guide covers the migration from the legacy FUS CMS architecture to the new modern stack. The migration includes database layer, validation layer, queue system, and security enhancements.

## Migration Summary

### Technology Changes

| Component | From | To | Status |
|-----------|------|----|---------| 
| ORM | Sequelize | Drizzle | ✅ Complete |
| Validation | express-validator | Zod | ✅ Complete |
| Queue System | Bull | BullMQ | ✅ Complete |
| Architecture | Mixed | MVC Pattern | ✅ Complete |
| Security | Basic | Enhanced JWT + Rate Limiting | ✅ Complete |

## Pre-Migration Checklist

- [ ] Backup existing database
- [ ] Document current API endpoints
- [ ] Test existing functionality
- [ ] Prepare rollback plan
- [ ] Set up monitoring
- [ ] Notify stakeholders

## Step-by-Step Migration

### 1. Database Migration (Sequelize → Drizzle)

#### 1.1 Install Dependencies

```bash
npm install drizzle-orm mysql2
npm install -D drizzle-kit
npm uninstall sequelize sequelize-cli mysql2-sequelize
```

#### 1.2 Update Database Configuration

**Before (Sequelize):**
```javascript
// config/database.js
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(process.env.DATABASE_URL);
```

**After (Drizzle):**
```typescript
// src/config/database.ts
import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';

const connection = await mysql.createConnection({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
});

export const db = drizzle(connection);
```

#### 1.3 Convert Models to Schemas

**Before (Sequelize Model):**
```javascript
// models/Game.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('Game', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  });
};
```

**After (Drizzle Schema):**
```typescript
// src/models/schemas/games.ts
import { mysqlTable, int, varchar, timestamp } from 'drizzle-orm/mysql-core';

export const games = mysqlTable('games', {
  id: int('id').primaryKey().autoincrement(),
  code: varchar('code', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').onUpdateNow(),
});

export type Game = typeof games.$inferSelect;
export type NewGame = typeof games.$inferInsert;
```

#### 1.4 Update Repository Pattern

**Before (Direct Sequelize):**
```javascript
// controllers/gameController.js
const { Game } = require('../models');

const getGames = async (req, res) => {
  const games = await Game.findAll();
  res.json(games);
};
```

**After (Repository Pattern):**
```typescript
// src/repositories/GameRepository.ts
export class GameRepository extends BaseRepository<Game> {
  async findAll(): Promise<Game[]> {
    return await this.db.select().from(games);
  }
}

// src/services/GameService.ts
export class GameService extends BaseService {
  async getGames(): Promise<Game[]> {
    return await this.gameRepository.findAll();
  }
}
```

### 2. Validation Migration (express-validator → Zod)

#### 2.1 Install Dependencies

```bash
npm install zod
npm uninstall express-validator
```

#### 2.2 Convert Validation Rules

**Before (express-validator):**
```javascript
// validators/gameValidator.js
const { body, validationResult } = require('express-validator');

const createGameValidator = [
  body('fid').notEmpty().withMessage('FID is required'),
  body('data').isArray().withMessage('Data must be an array'),
  body('data.*.code').notEmpty().withMessage('Game code is required'),
  body('data.*.name').notEmpty().withMessage('Game name is required'),
];
```

**After (Zod):**
```typescript
// src/validators/website/game.validator.ts
import { z } from 'zod';

export const createGameSchema = z.object({
  body: z.object({
    fid: z.string().min(1, 'FID is required'),
    data: z.array(z.object({
      code: z.string().min(1, 'Game code is required'),
      name: z.string().min(1, 'Game name is required'),
    })).min(1, 'At least one game is required'),
  }),
});

export type CreateGameRequest = z.infer<typeof createGameSchema>;
```

#### 2.3 Update Middleware

**Before:**
```javascript
// middleware/validation.js
const handleValidation = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};
```

**After:**
```typescript
// src/middlewares/validation/zod.middleware.ts
export const validate = (schema: ZodSchema) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      const validatedData = await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      
      Object.assign(req, validatedData);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = transformZodError(error);
        return next(new ValidationError(validationErrors));
      }
      next(error);
    }
  };
};
```

### 3. Queue System Migration (Bull → BullMQ)

#### 3.1 Install Dependencies

```bash
npm install bullmq
npm uninstall bull
```

#### 3.2 Update Queue Configuration

**Before (Bull):**
```javascript
// queues/gameReportQueue.js
const Queue = require('bull');

const gameReportQueue = new Queue('game report', {
  redis: { port: 6379, host: '127.0.0.1' }
});

gameReportQueue.process(async (job) => {
  // Process job
});
```

**After (BullMQ):**
```typescript
// src/queues/jobs/gameReport.job.ts
import { Job } from 'bullmq';

export interface GameReportJobData {
  fid: string;
  code: string;
  month: number;
  year: number;
}

export const processGameReport = async (job: Job<GameReportJobData>): Promise<void> => {
  const { fid, code, month, year } = job.data;
  // Process job
};

// src/queues/index.ts
import { Queue } from 'bullmq';

export const gameReportQueue = new Queue<GameReportJobData>('gameReport', {
  connection: queueRedisClient,
});
```

#### 3.3 Update Job Processing

**Before:**
```javascript
// Add job
gameReportQueue.add('update-report', { fid, code, month, year });
```

**After:**
```typescript
// Add job
await QueueService.addGameReportJob({ fid, code, month, year });
```

### 4. Controller Refactoring

#### 4.1 Update Controller Structure

**Before:**
```javascript
// controllers/gameController.js
const getGames = async (req, res) => {
  try {
    const games = await Game.findAll();
    res.json({ success: true, data: games });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};
```

**After:**
```typescript
// src/controllers/website/v1/GameController.ts
export class GameController extends BaseController {
  getListGames = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const { query } = req as GetGamesListRequest;
        return await this.gameService.getListGames(query);
      },
      'Games retrieved successfully'
    );
  });
}
```

### 5. Security Enhancements

#### 5.1 JWT Implementation

**Before (Basic Auth):**
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const token = req.headers['authorization'];
  if (!token) return res.sendStatus(401);
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};
```

**After (Enhanced JWT):**
```typescript
// src/middlewares/auth/jwt.middleware.ts
export class JWTMiddleware {
  authenticate = async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader?.startsWith('Bearer ')) {
        throw new UnauthorizedError('Missing or invalid authorization header');
      }

      const token = authHeader.substring(7);
      const payload = this.verifyToken(token);
      
      // Check blacklist, get user, verify permissions
      const user = await this.getUserFromPayload(payload);
      if (!user || user.status !== 1) {
        throw new UnauthorizedError('User not found or inactive');
      }

      req.user = user;
      next();
    } catch (error) {
      next(error);
    }
  };
}
```

#### 5.2 Rate Limiting

```typescript
// src/middlewares/security/rateLimiter.middleware.ts
export const apiLimiter = RateLimiterMiddleware.createAPILimiter(cacheService);
export const authLimiter = RateLimiterMiddleware.createAuthLimiter(cacheService);

// Apply to routes
app.use('/api', apiLimiter);
app.use('/api/auth', authLimiter);
```

## Testing Migration

### 1. Update Test Structure

**Before:**
```javascript
// test/game.test.js
const request = require('supertest');
const app = require('../app');

describe('Game API', () => {
  it('should get games', async () => {
    const res = await request(app).get('/api/games');
    expect(res.status).toBe(200);
  });
});
```

**After:**
```typescript
// tests/integration/controllers/GameController.test.ts
import { describe, it, expect } from 'vitest';
import request from 'supertest';
import { createTestApp } from '../../helpers/testApp';

describe('GameController Integration Tests', () => {
  let app: Express;
  let authToken: string;

  beforeAll(async () => {
    app = createTestApp();
    authToken = await generateTestToken();
  });

  it('should return list of games', async () => {
    const response = await request(app)
      .get('/api/v1/game')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body).toMatchObject({
      status: 'success',
      code: 200,
      metadata: expect.any(Array),
    });
  });
});
```

## Deployment Migration

### 1. Environment Variables

Update your environment variables:

```env
# Database (Updated)
DATABASE_URL=mysql://user:password@localhost:3306/database
DB_HOST=localhost
DB_USER=username
DB_PASSWORD=password
DB_NAME=database

# JWT (New)
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=fus-cms
JWT_AUDIENCE=fus-cms-api

# Redis (Updated for BullMQ)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_KEY_PREFIX=fus-cms:

# Rate Limiting (New)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
```

### 2. Docker Configuration

Update your Dockerfile and docker-compose.yml:

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY drizzle ./drizzle

EXPOSE 3000

CMD ["node", "dist/server.js"]
```

### 3. Database Migration Scripts

```bash
# Generate migration
npm run db:generate

# Run migration
npm run db:migrate

# Seed data
npm run db:seed
```

## Rollback Plan

### 1. Database Rollback

```bash
# Rollback to previous migration
npm run db:rollback

# Restore from backup
mysql -u username -p database_name < backup.sql
```

### 2. Code Rollback

```bash
# Revert to previous version
git revert <commit-hash>

# Deploy previous version
docker pull your-registry/fus-cms:previous-tag
docker-compose up -d
```

## Post-Migration Verification

### 1. Functionality Testing

- [ ] All API endpoints respond correctly
- [ ] Authentication works properly
- [ ] Database operations are successful
- [ ] Queue jobs are processing
- [ ] Rate limiting is active
- [ ] Error handling works correctly

### 2. Performance Testing

- [ ] Response times are acceptable
- [ ] Database queries are optimized
- [ ] Cache is working effectively
- [ ] Memory usage is stable
- [ ] Queue processing is efficient

### 3. Security Testing

- [ ] JWT tokens are properly validated
- [ ] Rate limiting prevents abuse
- [ ] Input validation catches malicious data
- [ ] Error messages don't leak sensitive info
- [ ] HTTPS is enforced

## Monitoring and Maintenance

### 1. Set Up Monitoring

```typescript
// Health check endpoint
app.get('/health-check', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseHealth(),
      redis: await checkRedisHealth(),
      queues: await checkQueueHealth(),
    },
  };
  
  res.json(health);
});
```

### 2. Queue Dashboard

Access BullMQ dashboard at `/admin/queues` for monitoring background jobs.

### 3. Logging

Implement structured logging:

```typescript
import winston from 'winston';

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check connection string format
   - Verify credentials and permissions
   - Ensure database server is running

2. **Queue Jobs Not Processing**
   - Check Redis connection
   - Verify worker processes are running
   - Check job data format

3. **Authentication Failures**
   - Verify JWT secret configuration
   - Check token expiration settings
   - Validate user permissions

4. **Rate Limiting Too Strict**
   - Adjust rate limit configuration
   - Check Redis key expiration
   - Verify client identification logic

### Getting Help

- Check logs: `docker-compose logs -f api`
- Monitor queues: Visit `/admin/queues`
- Health check: Visit `/health-check`
- API docs: Visit `/api/docs`

## Conclusion

This migration guide provides a comprehensive approach to upgrading FUS CMS to the new architecture. Follow each step carefully and test thoroughly at each stage. The new system provides better performance, security, and maintainability.
