import { BaseService } from '../base/BaseService';
import { TrackingRepository } from '../../repositories/website/TrackingRepository';
import { CacheService } from '../../config/redis';
import { QueueService } from '../../queues';
import { PaginatedResult, PaginationParams } from '../../types/database';

export class TrackingService extends BaseService {
  private trackingRepository: TrackingRepository;

  constructor(
    trackingRepository: TrackingRepository,
    cacheService: CacheService,
    queueService: typeof QueueService = QueueService
  ) {
    super(trackingRepository, cacheService, queueService);
    this.trackingRepository = trackingRepository;
  }

  // Track link click
  async trackLink(
    data: { link: string; type: number; fid?: string; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackLink', async () => {
      const currentDate = new Date();
      
      // Queue link report update
      await this.queueService.addLinkReportJob({
        link: data.link,
        type: data.type,
        fid: data.fid || 'unknown',
        month: currentDate.getMonth() + 1,
        year: currentDate.getFullYear(),
      });

      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        eventType: 'link_click',
        eventData: {
          link: data.link,
          type: data.type,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Link tracking recorded successfully',
      };
    });
  }

  // Track banner click
  async trackBanner(
    data: { bannerId: number; fid?: string; type?: number; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackBanner', async () => {
      const currentDate = new Date();
      
      // Queue banner report update
      await this.queueService.addBannerReportJob({
        bannerId: data.bannerId,
        fid: data.fid || 'unknown',
        type: data.type || 1,
        currentDate: currentDate.toISOString().split('T')[0],
      });

      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        eventType: 'banner_click',
        eventData: {
          bannerId: data.bannerId,
          type: data.type || 1,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Banner tracking recorded successfully',
      };
    });
  }

  // Track video interaction
  async trackVideo(
    data: { videoId: number; fid?: string; playlistId?: number; type?: number; duration?: number; position?: number; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackVideo', async () => {
      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        eventType: 'video_interaction',
        eventData: {
          videoId: data.videoId,
          playlistId: data.playlistId,
          type: data.type || 1,
          duration: data.duration,
          position: data.position,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Video tracking recorded successfully',
      };
    });
  }

  // Track game interaction
  async trackGame(
    data: { gameCode: string; fid?: string; uid?: string; type?: number; score?: number; level?: number; duration?: number; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackGame', async () => {
      const currentDate = new Date();
      
      // Queue game report update if it's a game open event
      if (data.type === 1) { // 1 = open
        await this.queueService.addGameReportJob({
          fid: data.fid || 'unknown',
          code: data.gameCode,
          uid: data.uid,
          month: currentDate.getMonth() + 1,
          year: currentDate.getFullYear(),
        });
      }

      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        uid: data.uid,
        eventType: 'game_interaction',
        eventData: {
          gameCode: data.gameCode,
          type: data.type || 1,
          score: data.score,
          level: data.level,
          duration: data.duration,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Game tracking recorded successfully',
      };
    });
  }

  // Track page view
  async trackPageView(
    data: { page: string; fid?: string; uid?: string; referrer?: string; userAgent?: string; screenResolution?: string; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackPageView', async () => {
      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        uid: data.uid,
        eventType: 'page_view',
        eventData: {
          page: data.page,
          referrer: data.referrer,
          screenResolution: data.screenResolution,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent: userAgent || data.userAgent,
      });

      return {
        success: true,
        message: 'Page view tracking recorded successfully',
      };
    });
  }

  // Track custom event
  async trackEvent(
    data: { eventType: string; eventName: string; fid?: string; uid?: string; properties?: any; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackEvent', async () => {
      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        uid: data.uid,
        eventType: data.eventType,
        eventData: {
          eventName: data.eventName,
          properties: data.properties,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Event tracking recorded successfully',
      };
    });
  }

  // Bulk tracking
  async bulkTracking(
    data: { events: Array<{ type: string; data: any; timestamp?: number }>; fid?: string; uid?: string },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{
    successful: any[];
    failed: Array<{ item: any; error: string }>;
    total: number;
  }> {
    return this.measurePerformance('bulkTracking', async () => {
      const successful: any[] = [];
      const failed: Array<{ item: any; error: string }> = [];

      for (const event of data.events) {
        try {
          const eventData = {
            ...event.data,
            fid: data.fid,
            uid: data.uid,
          };

          switch (event.type) {
            case 'link':
              await this.trackLink(eventData, ipAddress, userAgent);
              break;
            case 'banner':
              await this.trackBanner(eventData, ipAddress, userAgent);
              break;
            case 'video':
              await this.trackVideo(eventData, ipAddress, userAgent);
              break;
            case 'game':
              await this.trackGame(eventData, ipAddress, userAgent);
              break;
            case 'page_view':
              await this.trackPageView(eventData, ipAddress, userAgent);
              break;
            case 'custom':
              await this.trackEvent(eventData, ipAddress, userAgent);
              break;
            default:
              throw new Error(`Unknown event type: ${event.type}`);
          }
          
          successful.push(event);
        } catch (error) {
          failed.push({
            item: event,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      return {
        successful,
        failed,
        total: data.events.length,
      };
    });
  }

  // Get analytics dashboard
  async getAnalyticsDashboard(filters: any): Promise<any> {
    return this.measurePerformance('getAnalyticsDashboard', async () => {
      const cacheKey = this.generateCacheKey('tracking:dashboard', JSON.stringify(filters));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.trackingRepository.getAnalyticsDashboard(filters);
      }, 1800); // 30 minutes cache
    });
  }

  // Get real-time analytics
  async getRealTimeAnalytics(filters: any): Promise<any> {
    return this.measurePerformance('getRealTimeAnalytics', async () => {
      const cacheKey = this.generateCacheKey('tracking:realtime', JSON.stringify(filters));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.trackingRepository.getRealTimeAnalytics(filters);
      }, 300); // 5 minutes cache
    });
  }

  // Get link reports
  async getLinkReports(filters: any, pagination: PaginationParams): Promise<PaginatedResult<any>> {
    return this.measurePerformance('getLinkReports', async () => {
      const cacheKey = this.generateCacheKey('tracking:link_reports', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        return await this.trackingRepository.getLinkReports(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Get banner reports
  async getBannerReports(filters: any, pagination: PaginationParams): Promise<PaginatedResult<any>> {
    return this.measurePerformance('getBannerReports', async () => {
      const cacheKey = this.generateCacheKey('tracking:banner_reports', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        return await this.trackingRepository.getBannerReports(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Get video reports
  async getVideoReports(filters: any, pagination: PaginationParams): Promise<PaginatedResult<any>> {
    return this.measurePerformance('getVideoReports', async () => {
      const cacheKey = this.generateCacheKey('tracking:video_reports', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        return await this.trackingRepository.getVideoReports(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Track conversion
  async trackConversion(
    data: { conversionType: string; conversionValue?: number; currency?: string; fid?: string; uid?: string; transactionId?: string; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackConversion', async () => {
      // Log conversion event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        uid: data.uid,
        eventType: 'conversion',
        eventData: {
          conversionType: data.conversionType,
          conversionValue: data.conversionValue,
          currency: data.currency,
          transactionId: data.transactionId,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'Conversion tracking recorded successfully',
      };
    });
  }

  // Track A/B test
  async trackABTest(
    data: { testId: string; variant: string; fid?: string; uid?: string; conversionType?: string; metadata?: any },
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('trackABTest', async () => {
      // Log A/B test event
      await this.queueService.addLogEventJob({
        fid: data.fid || 'unknown',
        uid: data.uid,
        eventType: 'ab_test',
        eventData: {
          testId: data.testId,
          variant: data.variant,
          conversionType: data.conversionType,
          metadata: data.metadata,
        },
        ipAddress,
        userAgent,
      });

      return {
        success: true,
        message: 'A/B test tracking recorded successfully',
      };
    });
  }

  // Get heatmap data
  async getHeatmapData(filters: any): Promise<any> {
    return this.measurePerformance('getHeatmapData', async () => {
      const cacheKey = this.generateCacheKey('tracking:heatmap', JSON.stringify(filters));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.trackingRepository.getHeatmapData(filters);
      }, 3600); // 1 hour cache
    });
  }

  // Get funnel analysis
  async getFunnelAnalysis(steps: string[], fid?: string, period?: string): Promise<any> {
    return this.measurePerformance('getFunnelAnalysis', async () => {
      const cacheKey = this.generateCacheKey('tracking:funnel', steps.join(','), fid || 'all', period || 'month');
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.trackingRepository.getFunnelAnalysis(steps, fid, period);
      }, 3600); // 1 hour cache
    });
  }

  // Get cohort analysis
  async getCohortAnalysis(filters: any): Promise<any> {
    return this.measurePerformance('getCohortAnalysis', async () => {
      const cacheKey = this.generateCacheKey('tracking:cohort', JSON.stringify(filters));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.trackingRepository.getCohortAnalysis(filters);
      }, 7200); // 2 hours cache
    });
  }
}
