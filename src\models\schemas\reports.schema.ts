import { mysqlTable, int, varchar, text, timestamp, index, primaryKey } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const gameReports = mysqlTable('game_report', {
  code: varchar('code', { length: 255 }).notNull(),
  fid: varchar('fid', { length: 255 }).notNull(),
  month: int('month').notNull(),
  year: int('year').notNull(),
  totalOpen: int('total_open').notNull().default(0),
}, (table) => ({
  pk: primaryKey({ columns: [table.code, table.fid, table.month, table.year] }),
  codeIdx: index('idx_game_report_code').on(table.code),
  fidIdx: index('idx_game_report_fid').on(table.fid),
  dateIdx: index('idx_game_report_date').on(table.year, table.month),
}));

export const linkReports = mysqlTable('link_report', {
  link: varchar('link', { length: 500 }).notNull(),
  type: int('type').notNull(),
  fid: varchar('fid', { length: 255 }).notNull(),
  month: int('month').notNull(),
  year: int('year').notNull(),
  totalOpen: int('total_open').notNull().default(0),
}, (table) => ({
  pk: primaryKey({ columns: [table.link, table.type, table.fid, table.month, table.year] }),
  linkIdx: index('idx_link_report_link').on(table.link),
  typeIdx: index('idx_link_report_type').on(table.type),
  fidIdx: index('idx_link_report_fid').on(table.fid),
  dateIdx: index('idx_link_report_date').on(table.year, table.month),
}));

export const bannerReports = mysqlTable('banner_report', {
  bannerId: int('banner_id').notNull(),
  fid: varchar('fid', { length: 255 }).notNull(),
  type: int('type').notNull(),
  month: int('month').notNull(),
  year: int('year').notNull(),
  data: text('data').notNull(),
  totalOpen: int('total_open').notNull().default(0),
}, (table) => ({
  pk: primaryKey({ columns: [table.bannerId, table.fid, table.type, table.month, table.year] }),
  bannerIdx: index('idx_banner_report_banner').on(table.bannerId),
  fidIdx: index('idx_banner_report_fid').on(table.fid),
  typeIdx: index('idx_banner_report_type').on(table.type),
  dateIdx: index('idx_banner_report_date').on(table.year, table.month),
}));

export const userGameReports = mysqlTable('user_game_report', {
  fid: varchar('fid', { length: 255 }).notNull(),
  uid: varchar('uid', { length: 255 }).notNull(),
  games: text('games').notNull(), // JSON string
}, (table) => ({
  pk: primaryKey({ columns: [table.fid, table.uid] }),
  fidIdx: index('idx_user_game_report_fid').on(table.fid),
  uidIdx: index('idx_user_game_report_uid').on(table.uid),
}));

export const userH5GameReports = mysqlTable('user_h5_game_report', {
  fid: varchar('fid', { length: 255 }).notNull(),
  uid: varchar('uid', { length: 255 }).notNull(),
  code: varchar('code', { length: 255 }).notNull(),
  month: int('month').notNull(),
  year: int('year').notNull(),
  totalOpen: int('total_open').notNull().default(0),
}, (table) => ({
  pk: primaryKey({ columns: [table.fid, table.uid, table.code, table.month, table.year] }),
  fidIdx: index('idx_user_h5_game_report_fid').on(table.fid),
  uidIdx: index('idx_user_h5_game_report_uid').on(table.uid),
  codeIdx: index('idx_user_h5_game_report_code').on(table.code),
  dateIdx: index('idx_user_h5_game_report_date').on(table.year, table.month),
}));

export type GameReport = typeof gameReports.$inferSelect;
export type NewGameReport = typeof gameReports.$inferInsert;
export type LinkReport = typeof linkReports.$inferSelect;
export type NewLinkReport = typeof linkReports.$inferInsert;
export type BannerReport = typeof bannerReports.$inferSelect;
export type NewBannerReport = typeof bannerReports.$inferInsert;
export type UserGameReport = typeof userGameReports.$inferSelect;
export type NewUserGameReport = typeof userGameReports.$inferInsert;
export type UserH5GameReport = typeof userH5GameReports.$inferSelect;
export type NewUserH5GameReport = typeof userH5GameReports.$inferInsert;
