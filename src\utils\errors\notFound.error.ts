import { APIError } from './base.error';

export class NotFoundError extends APIError {
  public readonly statusCode = 404;
  public readonly isOperational = true;

  constructor(message: string = 'Resource not found') {
    super(message);
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, NotFoundError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
    };
  }
}
