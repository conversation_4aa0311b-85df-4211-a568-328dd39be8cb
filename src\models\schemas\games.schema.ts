import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const games = mysqlTable('games', {
  id: int('id').primaryKey().autoincrement(),
  code: varchar('code', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  logo: varchar('logo', { length: 500 }),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  codeIdx: index('idx_games_code').on(table.code),
  createdAtIdx: index('idx_games_created_at').on(table.createdAt),
}));

export type Game = typeof games.$inferSelect;
export type NewGame = typeof games.$inferInsert;
