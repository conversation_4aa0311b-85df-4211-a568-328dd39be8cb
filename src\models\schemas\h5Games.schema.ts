import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const h5Games = mysqlTable('h5_games', {
  id: int('id').primaryKey().autoincrement(),
  code: varchar('code', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  logo: varchar('logo', { length: 500 }),
  description: text('description'),
  url: text('url').notNull(),
  status: int('status').notNull().default(1),
  isTrending: int('is_trending').notNull().default(0),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  codeIdx: index('idx_h5_games_code').on(table.code),
  statusIdx: index('idx_h5_games_status').on(table.status),
  trendingIdx: index('idx_h5_games_trending').on(table.isTrending),
}));

export const h5GameCategories = mysqlTable('h5_game_categories', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: text('description'),
  icon: varchar('icon', { length: 500 }),
  sort: int('sort'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  slugIdx: index('idx_h5_game_categories_slug').on(table.slug),
  statusIdx: index('idx_h5_game_categories_status').on(table.status),
  sortIdx: index('idx_h5_game_categories_sort').on(table.sort),
}));

export const h5GameCategoryMatches = mysqlTable('h5_game_category_match', {
  gameId: int('game_id').notNull(),
  categoryId: int('category_id').notNull(),
}, (table) => ({
  gameIdx: index('idx_h5_game_category_match_game').on(table.gameId),
  categoryIdx: index('idx_h5_game_category_match_category').on(table.categoryId),
  uniqueMatch: index('idx_h5_game_category_match_unique').on(table.gameId, table.categoryId),
}));

export const selfGames = mysqlTable('self_games', {
  id: int('id').primaryKey().autoincrement(),
  code: varchar('code', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  logo: varchar('logo', { length: 500 }),
  description: text('description'),
  url: text('url').notNull(),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  codeIdx: index('idx_self_games_code').on(table.code),
  statusIdx: index('idx_self_games_status').on(table.status),
}));

export type H5Game = typeof h5Games.$inferSelect;
export type NewH5Game = typeof h5Games.$inferInsert;
export type H5GameCategory = typeof h5GameCategories.$inferSelect;
export type NewH5GameCategory = typeof h5GameCategories.$inferInsert;
export type H5GameCategoryMatch = typeof h5GameCategoryMatches.$inferSelect;
export type NewH5GameCategoryMatch = typeof h5GameCategoryMatches.$inferInsert;
export type SelfGame = typeof selfGames.$inferSelect;
export type NewSelfGame = typeof selfGames.$inferInsert;
