import { relations } from 'drizzle-orm';
import {
  // Auth tables
  roles, permissions, rolePermissions, admins, adminRoles, modules,
  // Game tables
  games, gameReports, h5Games, h5GameCategories, h5GameCategoryMatches, selfGames,
  // Banner tables
  banners, bannersV2, bannerReports, bannerPositions, bannerTimeSlots,
  // Video tables
  videos, playlists, playlistVideos,
  // Location tables
  regions, cities, locations,
  // Client tables
  clients, clientProducts,
  // Report tables
  userGameReports, userH5GameReports, linkReports,
} from '../schemas';

// Auth Relations
export const rolesRelations = relations(roles, ({ many }) => ({
  rolePermissions: many(rolePermissions),
  adminRoles: many(adminRoles),
}));

export const permissionsRelations = relations(permissions, ({ one, many }) => ({
  module: one(modules, {
    fields: [permissions.moduleId],
    references: [modules.id],
  }),
  rolePermissions: many(rolePermissions),
}));

export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(roles, {
    fields: [rolePermissions.roleId],
    references: [roles.id],
  }),
  permission: one(permissions, {
    fields: [rolePermissions.permissionId],
    references: [permissions.id],
  }),
}));

export const adminsRelations = relations(admins, ({ many }) => ({
  adminRoles: many(adminRoles),
}));

export const adminRolesRelations = relations(adminRoles, ({ one }) => ({
  admin: one(admins, {
    fields: [adminRoles.adminId],
    references: [admins.id],
  }),
  role: one(roles, {
    fields: [adminRoles.roleId],
    references: [roles.id],
  }),
}));

export const modulesRelations = relations(modules, ({ many }) => ({
  permissions: many(permissions),
}));

// Game Relations
export const gamesRelations = relations(games, ({ many }) => ({
  gameReports: many(gameReports),
}));

export const gameReportsRelations = relations(gameReports, ({ one }) => ({
  game: one(games, {
    fields: [gameReports.code],
    references: [games.code],
  }),
}));

export const h5GamesRelations = relations(h5Games, ({ many }) => ({
  categoryMatches: many(h5GameCategoryMatches),
  userReports: many(userH5GameReports),
}));

export const h5GameCategoriesRelations = relations(h5GameCategories, ({ many }) => ({
  categoryMatches: many(h5GameCategoryMatches),
}));

export const h5GameCategoryMatchesRelations = relations(h5GameCategoryMatches, ({ one }) => ({
  game: one(h5Games, {
    fields: [h5GameCategoryMatches.gameId],
    references: [h5Games.id],
  }),
  category: one(h5GameCategories, {
    fields: [h5GameCategoryMatches.categoryId],
    references: [h5GameCategories.id],
  }),
}));

// Banner Relations
export const bannersRelations = relations(banners, ({ one, many }) => ({
  position: one(bannerPositions, {
    fields: [banners.positionId],
    references: [bannerPositions.id],
  }),
  timeSlots: many(bannerTimeSlots),
}));

export const bannersV2Relations = relations(bannersV2, ({ one, many }) => ({
  position: one(bannerPositions, {
    fields: [bannersV2.positionId],
    references: [bannerPositions.id],
  }),
  client: one(clients, {
    fields: [bannersV2.clientId],
    references: [clients.id],
  }),
  clientProduct: one(clientProducts, {
    fields: [bannersV2.clientProductId],
    references: [clientProducts.id],
  }),
  reports: many(bannerReports),
}));

export const bannerPositionsRelations = relations(bannerPositions, ({ many }) => ({
  banners: many(banners),
  bannersV2: many(bannersV2),
}));

export const bannerTimeSlotsRelations = relations(bannerTimeSlots, ({ one }) => ({
  banner: one(banners, {
    fields: [bannerTimeSlots.bannerId],
    references: [banners.id],
  }),
}));

export const bannerReportsRelations = relations(bannerReports, ({ one }) => ({
  banner: one(bannersV2, {
    fields: [bannerReports.bannerId],
    references: [bannersV2.id],
  }),
}));

// Video Relations
export const videosRelations = relations(videos, ({ many }) => ({
  playlistVideos: many(playlistVideos),
}));

export const playlistsRelations = relations(playlists, ({ many }) => ({
  playlistVideos: many(playlistVideos),
}));

export const playlistVideosRelations = relations(playlistVideos, ({ one }) => ({
  playlist: one(playlists, {
    fields: [playlistVideos.playlistId],
    references: [playlists.id],
  }),
  video: one(videos, {
    fields: [playlistVideos.videoId],
    references: [videos.id],
  }),
}));

// Location Relations
export const regionsRelations = relations(regions, ({ many }) => ({
  cities: many(cities),
  locations: many(locations),
}));

export const citiesRelations = relations(cities, ({ one, many }) => ({
  region: one(regions, {
    fields: [cities.regionId],
    references: [regions.id],
  }),
  locations: many(locations),
}));

export const locationsRelations = relations(locations, ({ one }) => ({
  region: one(regions, {
    fields: [locations.regionId],
    references: [regions.id],
  }),
  city: one(cities, {
    fields: [locations.cityId],
    references: [cities.id],
  }),
}));

// Client Relations
export const clientsRelations = relations(clients, ({ many }) => ({
  products: many(clientProducts),
  bannersV2: many(bannersV2),
}));

export const clientProductsRelations = relations(clientProducts, ({ one, many }) => ({
  client: one(clients, {
    fields: [clientProducts.clientId],
    references: [clients.id],
  }),
  bannersV2: many(bannersV2),
}));

// Report Relations
export const userH5GameReportsRelations = relations(userH5GameReports, ({ one }) => ({
  game: one(h5Games, {
    fields: [userH5GameReports.code],
    references: [h5Games.code],
  }),
}));
