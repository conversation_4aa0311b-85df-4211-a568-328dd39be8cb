import { BaseService } from '../base/BaseService';
import { GameRepository } from '../../repositories/website/GameRepository';
import { FusGameRepository } from '../../repositories/website/FusGameRepository';
import { CacheService } from '../../config/redis';
import { QueueService } from '../../queues';
import { PaginatedResult, PaginationParams } from '../../types/database';
import { Game, FusGame } from '../../models/schemas';
import { BadRequestError } from '../../utils/errors/badRequest.error';
import { NotFoundError } from '../../utils/errors/notFound.error';
import Parser from 'rss-parser';

export class GameService extends BaseService {
  private gameRepository: GameRepository;
  private fusGameRepository: FusGameRepository;
  private rssParser: Parser;

  constructor(
    gameRepository: GameRepository,
    fusGameRepository: FusGameRepository,
    cacheService: CacheService,
    queueService: typeof QueueService = QueueService
  ) {
    super(gameRepository, cacheService, queueService);
    this.gameRepository = gameRepository;
    this.fusGameRepository = fusGameRepository;
    this.rssParser = new Parser();
  }

  // Get list of games
  async getListGames(query: any): Promise<Game[]> {
    return this.measurePerformance('getListGames', async () => {
      const cacheKey = this.generateCacheKey('games:list', JSON.stringify(query));
      
      return this.getCachedList(cacheKey, async () => {
        const filters = this.buildGameFilters(query);
        return await this.gameRepository.findMany(filters, {
          limit: query.limit || 50,
          orderBy: this.gameRepository.buildOrderBy('createdAt', 'desc'),
        });
      }, 1800); // 30 minutes cache
    });
  }

  // Create new game
  async createGame(data: { fid: string; data: Array<{ code: string; name: string }> }): Promise<FusGame> {
    return this.measurePerformance('createGame', async () => {
      // Validate game codes don't already exist
      const existingCodes = await this.gameRepository.findByCodesInFid(
        data.data.map(g => g.code),
        data.fid
      );

      if (existingCodes.length > 0) {
        throw new BadRequestError(
          `Game codes already exist: ${existingCodes.map(g => g.code).join(', ')}`
        );
      }

      // Create games in games table
      const gamePromises = data.data.map(gameData => 
        this.gameRepository.create({
          code: gameData.code,
          name: gameData.name,
        })
      );

      await Promise.all(gamePromises);

      // Create or update FUS game entry
      const fusGameData = {
        fid: data.fid,
        data: data.data,
      };

      const result = await this.fusGameRepository.createOrUpdate(fusGameData);

      // Invalidate related caches
      await this.invalidateCache(`games:*`);
      await this.invalidateCache(`fus_games:${data.fid}:*`);

      return result;
    });
  }

  // Get top games by FID
  async getListTopGames(fid: string, query: any): Promise<PaginatedResult<any>> {
    return this.measurePerformance('getListTopGames', async () => {
      const cacheKey = this.generateCacheKey('games:top', fid, JSON.stringify(query));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        const { period = 'month', page = 1, limit = 10 } = query;
        
        return await this.gameRepository.getTopGamesByFid(fid, {
          period,
          page,
          limit,
        });
      }, 3600); // 1 hour cache
    });
  }

  // Get user games
  async getListUserGames(fid: string, uid: string, query: any): Promise<PaginatedResult<any>> {
    return this.measurePerformance('getListUserGames', async () => {
      const cacheKey = this.generateCacheKey('games:user', fid, uid, JSON.stringify(query));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        const { period = 'month', page = 1, limit = 10 } = query;
        
        return await this.gameRepository.getUserGames(fid, uid, {
          period,
          page,
          limit,
        });
      }, 1800); // 30 minutes cache
    });
  }

  // Get game detail
  async getGameDetail(code: string): Promise<Game | null> {
    return this.measurePerformance('getGameDetail', async () => {
      const cacheKey = this.generateCacheKey('games:detail', code);
      
      return this.getCachedDetail(cacheKey, async () => {
        return await this.gameRepository.findByCode(code);
      }, 3600); // 1 hour cache
    });
  }

  // Open game
  async openGame(data: { code: string; fid: string; uid?: string; metadata?: any }): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('openGame', async () => {
      // Verify game exists
      const game = await this.gameRepository.findByCode(data.code);
      if (!game) {
        throw new NotFoundError('Game not found');
      }

      // Queue game report update
      const currentDate = new Date();
      await this.queueService.addGameReportJob({
        fid: data.fid,
        code: data.code,
        uid: data.uid,
        month: currentDate.getMonth() + 1,
        year: currentDate.getFullYear(),
      });

      // Log event
      await this.queueService.addLogEventJob({
        fid: data.fid,
        uid: data.uid,
        eventType: 'game_open',
        eventData: {
          gameCode: data.code,
          metadata: data.metadata,
        },
      });

      return {
        success: true,
        message: 'Game opened successfully',
      };
    });
  }

  // Get RSS data
  async getListRss(link: string): Promise<any> {
    return this.measurePerformance('getListRss', async () => {
      const cacheKey = this.generateCacheKey('rss', Buffer.from(link).toString('base64'));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        try {
          const feed = await this.rssParser.parseURL(link);
          return {
            title: feed.title,
            description: feed.description,
            link: feed.link,
            items: feed.items.slice(0, 20).map(item => ({
              title: item.title,
              link: item.link,
              pubDate: item.pubDate,
              contentSnippet: item.contentSnippet?.substring(0, 200),
            })),
          };
        } catch (error) {
          throw new BadRequestError('Failed to parse RSS feed');
        }
      }, 3600); // 1 hour cache
    });
  }

  // Update RSS feeds
  async updateRss(): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('updateRss', async () => {
      // This would typically update RSS feeds from a configured list
      // For now, return a success message
      return {
        success: true,
        message: 'RSS feeds updated successfully',
      };
    });
  }

  // Get game statistics
  async getGameStats(filters: any, pagination: PaginationParams): Promise<any> {
    return this.measurePerformance('getGameStats', async () => {
      const cacheKey = this.generateCacheKey('games:stats', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.gameRepository.getGameStats(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Bulk game operations
  async bulkGameOperation(
    operation: string,
    codes: string[],
    reason?: string
  ): Promise<{
    successful: string[];
    failed: Array<{ item: string; error: string }>;
    total: number;
  }> {
    return this.measurePerformance('bulkGameOperation', async () => {
      const successful: string[] = [];
      const failed: Array<{ item: string; error: string }> = [];

      for (const code of codes) {
        try {
          switch (operation) {
            case 'activate':
              await this.gameRepository.updateStatus(code, 1);
              break;
            case 'deactivate':
              await this.gameRepository.updateStatus(code, 0);
              break;
            case 'delete':
              await this.gameRepository.deleteByCode(code);
              break;
            default:
              throw new Error(`Unknown operation: ${operation}`);
          }
          successful.push(code);
        } catch (error) {
          failed.push({
            item: code,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Invalidate caches if any operations were successful
      if (successful.length > 0) {
        await this.invalidateCache('games:*');
      }

      return {
        successful,
        failed,
        total: codes.length,
      };
    });
  }

  // Search games
  async searchGames(
    query: string,
    filters: any,
    pagination: PaginationParams,
    sort: { field: string; direction: 'asc' | 'desc' }
  ): Promise<PaginatedResult<Game>> {
    return this.measurePerformance('searchGames', async () => {
      const cacheKey = this.generateCacheKey('games:search', query, JSON.stringify(filters), JSON.stringify(pagination), JSON.stringify(sort));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        return await this.gameRepository.searchGames(query, filters, pagination, sort);
      }, 900); // 15 minutes cache
    });
  }

  // Get game categories
  async getGameCategories(filters: any): Promise<any[]> {
    return this.measurePerformance('getGameCategories', async () => {
      const cacheKey = this.generateCacheKey('games:categories', JSON.stringify(filters));
      
      return this.getCachedList(cacheKey, async () => {
        // This would typically fetch from a game categories table
        // For now, return mock data
        return [
          { id: 1, name: 'Action', slug: 'action', count: 25 },
          { id: 2, name: 'Adventure', slug: 'adventure', count: 18 },
          { id: 3, name: 'Puzzle', slug: 'puzzle', count: 32 },
          { id: 4, name: 'Strategy', slug: 'strategy', count: 15 },
        ];
      }, 7200); // 2 hours cache
    });
  }

  // Get trending games
  async getTrendingGames(limit: number, period: string): Promise<Game[]> {
    return this.measurePerformance('getTrendingGames', async () => {
      const cacheKey = this.generateCacheKey('games:trending', limit.toString(), period);
      
      return this.getCachedList(cacheKey, async () => {
        return await this.gameRepository.getTrendingGames(limit, period);
      }, 3600); // 1 hour cache
    });
  }

  // Get game analytics
  async getGameAnalytics(filters: any, pagination: PaginationParams): Promise<any> {
    return this.measurePerformance('getGameAnalytics', async () => {
      const cacheKey = this.generateCacheKey('games:analytics', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.gameRepository.getGameAnalytics(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Helper methods
  private buildGameFilters(query: any): Partial<Game> {
    const filters: Partial<Game> = {};

    if (query.status !== undefined) {
      filters.status = parseInt(query.status);
    }

    return filters;
  }
}
