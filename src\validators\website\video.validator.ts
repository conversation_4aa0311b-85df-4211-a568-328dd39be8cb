import { z } from 'zod';
import {
  idSchema,
  paginationSchema,
  baseQuerySchema,
  paramsWithIdSchema,
  urlSchema,
  shortTextSchema,
  mediumTextSchema,
  longTextSchema,
  statusSchema,
  slugSchema,
} from '../common/base.validator';

// Video type schema
export const videoTypeSchema = z.number().int().min(1).max(10);

// Video duration schema (in format MM:SS or HH:MM:SS)
export const videoDurationSchema = z.string().regex(
  /^(?:(?:([01]?\d|2[0-3]):)?([0-5]?\d):)?([0-5]?\d)$/,
  'Duration must be in MM:SS or HH:MM:SS format'
);

// Video year schema
export const videoYearSchema = z.number().int().min(1900).max(new Date().getFullYear() + 1);

// Create video request
export const createVideoSchema = z.object({
  body: z.object({
    thumbnail: urlSchema,
    title: shortTextSchema,
    iframeUrl: urlSchema,
    duration: videoDurationSchema.optional(),
    link: urlSchema,
    type: videoTypeSchema,
    author: shortTextSchema.optional(),
    year: videoYearSchema.optional(),
    status: statusSchema.default(1),
    publishDate: z.string().datetime().optional(),
  }),
});

// Update video request
export const updateVideoSchema = z.object({
  params: paramsWithIdSchema,
  body: z.object({
    thumbnail: urlSchema.optional(),
    title: shortTextSchema.optional(),
    iframeUrl: urlSchema.optional(),
    duration: videoDurationSchema.optional(),
    link: urlSchema.optional(),
    type: videoTypeSchema.optional(),
    author: shortTextSchema.optional(),
    year: videoYearSchema.optional(),
    status: statusSchema.optional(),
    publishDate: z.string().datetime().optional(),
  }),
});

// Get videos list query
export const getVideosListSchema = z.object({
  query: baseQuerySchema.extend({
    playlistSlug: slugSchema.optional(),
    type: videoTypeSchema.optional(),
    author: z.string().max(255).optional(),
    year: videoYearSchema.optional(),
    sortBy: z.enum(['title', 'created_at', 'publish_date', 'views']).default('created_at'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    offset: z.coerce.number().int().min(0).optional(),
  }),
});

// Get latest videos query
export const getLatestVideosSchema = z.object({
  query: z.object({
    playlistSlug: slugSchema.optional(),
    search: z.string().max(255).optional(),
    offset: z.coerce.number().int().min(0).optional(),
    limit: z.coerce.number().int().min(1).max(100).default(10),
  }),
});

// Get top view videos query
export const getTopViewVideosSchema = z.object({
  query: z.object({
    playlistSlug: slugSchema.optional(),
    period: z.enum(['day', 'week', 'month', 'year', 'all']).default('month'),
    ...paginationSchema.shape,
  }),
});

// Get video detail params
export const getVideoDetailSchema = z.object({
  params: paramsWithIdSchema,
});

// Playlist schemas
export const createPlaylistSchema = z.object({
  body: z.object({
    name: shortTextSchema,
    icon: urlSchema.optional(),
    slug: slugSchema,
    description: mediumTextSchema.optional(),
    sort: z.number().int().min(0).optional(),
    status: statusSchema.default(1),
  }),
});

export const updatePlaylistSchema = z.object({
  params: paramsWithIdSchema,
  body: z.object({
    name: shortTextSchema.optional(),
    icon: urlSchema.optional(),
    slug: slugSchema.optional(),
    description: mediumTextSchema.optional(),
    sort: z.number().int().min(0).optional(),
    status: statusSchema.optional(),
  }),
});

export const getPlaylistsListSchema = z.object({
  query: baseQuerySchema.extend({
    sortBy: z.enum(['name', 'created_at', 'sort', 'video_count']).default('sort'),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
  }),
});

export const getPlaylistDetailSchema = z.object({
  params: z.object({
    slug: slugSchema,
  }),
});

// Playlist video management
export const addVideoToPlaylistSchema = z.object({
  body: z.object({
    playlistId: idSchema,
    videoIds: z.array(idSchema).min(1, 'At least one video ID is required').max(100),
  }),
});

export const removeVideoFromPlaylistSchema = z.object({
  body: z.object({
    playlistId: idSchema,
    videoIds: z.array(idSchema).min(1, 'At least one video ID is required').max(100),
  }),
});

export const reorderPlaylistVideosSchema = z.object({
  body: z.object({
    playlistId: idSchema,
    videoOrders: z.array(z.object({
      videoId: idSchema,
      order: z.number().int().min(1),
    })).min(1, 'At least one video order is required'),
  }),
});

// Video search schema
export const videoSearchSchema = z.object({
  query: z.object({
    q: z.string().min(1, 'Search query is required').max(100),
    playlistSlug: slugSchema.optional(),
    type: videoTypeSchema.optional(),
    author: z.string().max(255).optional(),
    year: videoYearSchema.optional(),
    status: z.enum(['0', '1']).optional(),
    sortBy: z.enum(['title', 'created_at', 'publish_date', 'relevance']).default('relevance'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    ...paginationSchema.shape,
  }),
});

// Video analytics schema
export const videoAnalyticsSchema = z.object({
  query: z.object({
    videoIds: z.string().transform((val) => val.split(',')).pipe(z.array(idSchema)).optional(),
    playlistId: idSchema.optional(),
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
    metrics: z.string().transform((val) => val.split(',')).pipe(
      z.array(z.enum(['views', 'unique_views', 'watch_time', 'completion_rate', 'likes', 'shares']))
    ).default(['views']),
    groupBy: z.enum(['video', 'playlist', 'author', 'date']).default('video'),
    ...paginationSchema.shape,
  }),
});

// Bulk video operations
export const bulkVideoOperationSchema = z.object({
  body: z.object({
    operation: z.enum(['activate', 'deactivate', 'delete', 'move_to_playlist']),
    videoIds: z.array(idSchema).min(1, 'At least one video ID is required').max(100),
    targetPlaylistId: idSchema.optional(), // Required when operation is 'move_to_playlist'
    reason: z.string().max(500).optional(),
  }),
});

// Video upload schema
export const videoUploadSchema = z.object({
  body: z.object({
    title: shortTextSchema,
    description: longTextSchema.optional(),
    tags: z.array(z.string().max(50)).max(10).optional(),
    playlistId: idSchema.optional(),
    publishDate: z.string().datetime().optional(),
    visibility: z.enum(['public', 'private', 'unlisted']).default('public'),
  }),
});

// Video metadata schema
export const videoMetadataSchema = z.object({
  body: z.object({
    videoId: idSchema,
    metadata: z.object({
      genre: z.array(z.string().max(50)).max(5).optional(),
      language: z.string().length(2).optional(), // ISO language code
      subtitles: z.array(z.object({
        language: z.string().length(2),
        url: urlSchema,
      })).max(10).optional(),
      chapters: z.array(z.object({
        title: z.string().max(100),
        startTime: z.number().int().min(0), // in seconds
        endTime: z.number().int().min(0), // in seconds
      })).max(50).optional(),
      quality: z.array(z.enum(['240p', '360p', '480p', '720p', '1080p', '4K'])).optional(),
      aspectRatio: z.enum(['16:9', '4:3', '1:1', '9:16']).optional(),
      frameRate: z.number().int().min(1).max(120).optional(),
    }),
  }),
});

// Export types
export type CreateVideoRequest = z.infer<typeof createVideoSchema>;
export type UpdateVideoRequest = z.infer<typeof updateVideoSchema>;
export type GetVideosListRequest = z.infer<typeof getVideosListSchema>;
export type GetLatestVideosRequest = z.infer<typeof getLatestVideosSchema>;
export type GetTopViewVideosRequest = z.infer<typeof getTopViewVideosSchema>;
export type GetVideoDetailRequest = z.infer<typeof getVideoDetailSchema>;
export type CreatePlaylistRequest = z.infer<typeof createPlaylistSchema>;
export type UpdatePlaylistRequest = z.infer<typeof updatePlaylistSchema>;
export type GetPlaylistsListRequest = z.infer<typeof getPlaylistsListSchema>;
export type GetPlaylistDetailRequest = z.infer<typeof getPlaylistDetailSchema>;
export type AddVideoToPlaylistRequest = z.infer<typeof addVideoToPlaylistSchema>;
export type RemoveVideoFromPlaylistRequest = z.infer<typeof removeVideoFromPlaylistSchema>;
export type ReorderPlaylistVideosRequest = z.infer<typeof reorderPlaylistVideosSchema>;
export type VideoSearchRequest = z.infer<typeof videoSearchSchema>;
export type VideoAnalyticsRequest = z.infer<typeof videoAnalyticsSchema>;
export type BulkVideoOperationRequest = z.infer<typeof bulkVideoOperationSchema>;
export type VideoUploadRequest = z.infer<typeof videoUploadSchema>;
export type VideoMetadataRequest = z.infer<typeof videoMetadataSchema>;
