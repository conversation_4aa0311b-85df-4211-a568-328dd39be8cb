import { z } from 'zod';
import {
  idSchema,
  fidSchema,
  urlSchema,
  paginationSchema,
  dateStringSchema,
} from '../common/base.validator';

// Link types enum
export const linkTypeSchema = z.number().int().min(1).max(10);

// Banner types enum
export const bannerTypeSchema = z.number().int().min(1).max(10);

// Video types enum
export const videoTypeSchema = z.number().int().min(1).max(10);

// Track link request
export const trackLinkSchema = z.object({
  body: z.object({
    link: urlSchema,
    type: linkTypeSchema,
    fid: fidSchema.optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// Track banner request
export const trackBannerSchema = z.object({
  body: z.object({
    bannerId: idSchema,
    fid: fidSchema.optional(),
    type: bannerTypeSchema.optional().default(1), // 1 = click, 2 = impression, etc.
    metadata: z.record(z.any()).optional(),
  }),
});

// Track video request
export const trackVideoSchema = z.object({
  body: z.object({
    videoId: idSchema,
    fid: fidSchema.optional(),
    playlistId: idSchema.optional(),
    type: videoTypeSchema.optional().default(1), // 1 = view, 2 = like, 3 = share, etc.
    duration: z.number().int().min(0).optional(), // Duration watched in seconds
    position: z.number().int().min(0).optional(), // Position where user stopped watching
    metadata: z.record(z.any()).optional(),
  }),
});

// Track game request
export const trackGameSchema = z.object({
  body: z.object({
    gameCode: z.string().min(1, 'Game code is required').max(100),
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
    type: z.number().int().min(1).max(10).default(1), // 1 = open, 2 = play, 3 = complete, etc.
    score: z.number().int().min(0).optional(),
    level: z.number().int().min(1).optional(),
    duration: z.number().int().min(0).optional(), // Duration played in seconds
    metadata: z.record(z.any()).optional(),
  }),
});

// Track page view request
export const trackPageViewSchema = z.object({
  body: z.object({
    page: z.string().min(1, 'Page is required').max(500),
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
    referrer: urlSchema.optional(),
    userAgent: z.string().max(1000).optional(),
    screenResolution: z.string().regex(/^\d+x\d+$/).optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// Track event request (generic)
export const trackEventSchema = z.object({
  body: z.object({
    eventType: z.string().min(1, 'Event type is required').max(100),
    eventName: z.string().min(1, 'Event name is required').max(200),
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
    properties: z.record(z.any()).optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// Bulk tracking request
export const bulkTrackingSchema = z.object({
  body: z.object({
    events: z.array(z.object({
      type: z.enum(['link', 'banner', 'video', 'game', 'page_view', 'custom']),
      data: z.record(z.any()),
      timestamp: z.number().int().positive().optional(),
    })).min(1, 'At least one event is required').max(100, 'Maximum 100 events allowed'),
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
  }),
});

// Link report filter
export const linkReportFilterSchema = z.object({
  query: z.object({
    link: urlSchema.optional(),
    type: linkTypeSchema.optional(),
    fid: fidSchema.optional(),
    month: z.coerce.number().int().min(1).max(12).optional(),
    year: z.coerce.number().int().min(2020).max(2030).optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    ...paginationSchema.shape,
  }),
});

// Banner report filter
export const bannerReportFilterSchema = z.object({
  query: z.object({
    bannerId: idSchema.optional(),
    type: bannerTypeSchema.optional(),
    fid: fidSchema.optional(),
    month: z.coerce.number().int().min(1).max(12).optional(),
    year: z.coerce.number().int().min(2020).max(2030).optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    ...paginationSchema.shape,
  }),
});

// Video report filter
export const videoReportFilterSchema = z.object({
  query: z.object({
    videoId: idSchema.optional(),
    playlistId: idSchema.optional(),
    type: videoTypeSchema.optional(),
    fid: fidSchema.optional(),
    month: z.coerce.number().int().min(1).max(12).optional(),
    year: z.coerce.number().int().min(2020).max(2030).optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    ...paginationSchema.shape,
  }),
});

// Analytics dashboard filter
export const analyticsDashboardSchema = z.object({
  query: z.object({
    fid: fidSchema.optional(),
    period: z.enum(['today', 'yesterday', 'last_7_days', 'last_30_days', 'this_month', 'last_month', 'custom']).default('last_7_days'),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    metrics: z.string().transform((val) => val.split(',')).pipe(
      z.array(z.enum(['page_views', 'unique_visitors', 'bounce_rate', 'avg_session_duration', 'top_pages', 'top_referrers']))
    ).default(['page_views', 'unique_visitors']),
    groupBy: z.enum(['hour', 'day', 'week', 'month']).default('day'),
  }),
});

// Real-time analytics
export const realTimeAnalyticsSchema = z.object({
  query: z.object({
    fid: fidSchema.optional(),
    limit: z.coerce.number().int().min(1).max(100).default(50),
    eventTypes: z.string().transform((val) => val.split(',')).pipe(
      z.array(z.string().max(100))
    ).optional(),
  }),
});

// Conversion tracking
export const conversionTrackingSchema = z.object({
  body: z.object({
    conversionType: z.string().min(1, 'Conversion type is required').max(100),
    conversionValue: z.number().min(0).optional(),
    currency: z.string().length(3).optional(), // ISO currency code
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
    transactionId: z.string().max(255).optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// A/B test tracking
export const abTestTrackingSchema = z.object({
  body: z.object({
    testId: z.string().min(1, 'Test ID is required').max(100),
    variant: z.string().min(1, 'Variant is required').max(50),
    fid: fidSchema.optional(),
    uid: z.string().max(255).optional(),
    conversionType: z.string().max(100).optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// Heatmap data request
export const heatmapDataSchema = z.object({
  query: z.object({
    page: z.string().min(1, 'Page is required').max(500),
    fid: fidSchema.optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    deviceType: z.enum(['desktop', 'mobile', 'tablet']).optional(),
    resolution: z.string().regex(/^\d+x\d+$/).optional(),
  }),
});

// Export types
export type TrackLinkRequest = z.infer<typeof trackLinkSchema>;
export type TrackBannerRequest = z.infer<typeof trackBannerSchema>;
export type TrackVideoRequest = z.infer<typeof trackVideoSchema>;
export type TrackGameRequest = z.infer<typeof trackGameSchema>;
export type TrackPageViewRequest = z.infer<typeof trackPageViewSchema>;
export type TrackEventRequest = z.infer<typeof trackEventSchema>;
export type BulkTrackingRequest = z.infer<typeof bulkTrackingSchema>;
export type LinkReportFilterRequest = z.infer<typeof linkReportFilterSchema>;
export type BannerReportFilterRequest = z.infer<typeof bannerReportFilterSchema>;
export type VideoReportFilterRequest = z.infer<typeof videoReportFilterSchema>;
export type AnalyticsDashboardRequest = z.infer<typeof analyticsDashboardSchema>;
export type RealTimeAnalyticsRequest = z.infer<typeof realTimeAnalyticsSchema>;
export type ConversionTrackingRequest = z.infer<typeof conversionTrackingSchema>;
export type ABTestTrackingRequest = z.infer<typeof abTestTrackingSchema>;
export type HeatmapDataRequest = z.infer<typeof heatmapDataSchema>;
