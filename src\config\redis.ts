import Redis from 'ioredis';
import * as dotenv from 'dotenv';

dotenv.config();

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'fus-cms:',
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnClusterDown: 300,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
};

// Main Redis client for caching
export const redisClient = new Redis(redisConfig);

// Separate Redis client for BullMQ (without keyPrefix to avoid conflicts)
export const queueRedisClient = new Redis({
  ...redisConfig,
  keyPrefix: `${process.env.REDIS_KEY_PREFIX || 'fus-cms:'}bull:`,
});

// Redis client for pub/sub
export const pubSubRedisClient = new Redis(redisConfig);

// Connection event handlers
redisClient.on('connect', () => {
  console.log('✅ Redis client connected');
});

redisClient.on('error', (error) => {
  console.error('❌ Redis client error:', error);
});

redisClient.on('ready', () => {
  console.log('✅ Redis client ready');
});

queueRedisClient.on('connect', () => {
  console.log('✅ Queue Redis client connected');
});

queueRedisClient.on('error', (error) => {
  console.error('❌ Queue Redis client error:', error);
});

pubSubRedisClient.on('connect', () => {
  console.log('✅ PubSub Redis client connected');
});

pubSubRedisClient.on('error', (error) => {
  console.error('❌ PubSub Redis client error:', error);
});

// Connection function
export const connectRedis = async (retries: number = 5): Promise<void> => {
  while (retries > 0) {
    try {
      await Promise.all([
        redisClient.ping(),
        queueRedisClient.ping(),
        pubSubRedisClient.ping(),
      ]);
      console.log('✅ All Redis clients connected successfully.');
      return;
    } catch (error) {
      console.error(`❌ Unable to connect to Redis. Retries left: ${retries}`, error);
      retries -= 1;
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5s before retry
      }
    }
  }
  throw new Error('❌ Redis connection failed after multiple retries.');
};

// Graceful shutdown
export const closeRedis = async (): Promise<void> => {
  try {
    await Promise.all([
      redisClient.quit(),
      queueRedisClient.quit(),
      pubSubRedisClient.quit(),
    ]);
    console.log('✅ All Redis connections closed.');
  } catch (error) {
    console.error('❌ Error closing Redis connections:', error);
  }
};

// Cache service wrapper
export class CacheService {
  private client: Redis;

  constructor(client: Redis = redisClient) {
    this.client = client;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await this.client.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await this.client.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
    } catch (error) {
      console.error('Cache invalidate pattern error:', error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async increment(key: string, ttl?: number): Promise<number> {
    try {
      const result = await this.client.incr(key);
      if (ttl && result === 1) {
        await this.client.expire(key, ttl);
      }
      return result;
    } catch (error) {
      console.error('Cache increment error:', error);
      return 0;
    }
  }

  async setHash(key: string, field: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.client.hset(key, field, JSON.stringify(value));
      if (ttl) {
        await this.client.expire(key, ttl);
      }
    } catch (error) {
      console.error('Cache set hash error:', error);
    }
  }

  async getHash<T>(key: string, field: string): Promise<T | null> {
    try {
      const cached = await this.client.hget(key, field);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Cache get hash error:', error);
      return null;
    }
  }

  async getAllHash<T>(key: string): Promise<Record<string, T>> {
    try {
      const cached = await this.client.hgetall(key);
      const result: Record<string, T> = {};
      for (const [field, value] of Object.entries(cached)) {
        result[field] = JSON.parse(value);
      }
      return result;
    } catch (error) {
      console.error('Cache get all hash error:', error);
      return {};
    }
  }

  async addToSet(key: string, value: string, ttl?: number): Promise<void> {
    try {
      await this.client.sadd(key, value);
      if (ttl) {
        await this.client.expire(key, ttl);
      }
    } catch (error) {
      console.error('Cache add to set error:', error);
    }
  }

  async getSet(key: string): Promise<string[]> {
    try {
      return await this.client.smembers(key);
    } catch (error) {
      console.error('Cache get set error:', error);
      return [];
    }
  }

  async removeFromSet(key: string, value: string): Promise<void> {
    try {
      await this.client.srem(key, value);
    } catch (error) {
      console.error('Cache remove from set error:', error);
    }
  }
}

// Export default cache service instance
export const cacheService = new CacheService();
