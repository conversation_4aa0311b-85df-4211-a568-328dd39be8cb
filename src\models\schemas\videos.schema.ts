import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const videos = mysqlTable('videos', {
  id: int('id').primaryKey().autoincrement(),
  thumbnail: text('thumbnail').notNull(),
  title: text('title').notNull(),
  iframeUrl: text('iframe_url').notNull(),
  duration: varchar('duration', { length: 50 }),
  link: text('link').notNull(),
  type: int('type').notNull(),
  author: text('author'),
  year: int('year'),
  status: int('status').notNull().default(1),
  publishDate: timestamp('publish_date'),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
  deletedAt: timestamp('deleted_at'),
  deletedBy: int('deleted_by'),
}, (table) => ({
  typeIdx: index('idx_videos_type').on(table.type),
  statusIdx: index('idx_videos_status').on(table.status),
  publishDateIdx: index('idx_videos_publish_date').on(table.publishDate),
  yearIdx: index('idx_videos_year').on(table.year),
  deletedAtIdx: index('idx_videos_deleted_at').on(table.deletedAt),
}));

export const playlists = mysqlTable('playlist', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  icon: varchar('icon', { length: 500 }),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: varchar('description', { length: 500 }),
  sort: int('sort'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
  deletedAt: timestamp('deleted_at'),
  deletedBy: int('deleted_by'),
}, (table) => ({
  slugIdx: index('idx_playlist_slug').on(table.slug),
  statusIdx: index('idx_playlist_status').on(table.status),
  sortIdx: index('idx_playlist_sort').on(table.sort),
  deletedAtIdx: index('idx_playlist_deleted_at').on(table.deletedAt),
}));

export const playlistVideos = mysqlTable('playlist_video', {
  playlistId: int('playlist_id').notNull(),
  videoId: int('video_id').notNull(),
}, (table) => ({
  playlistVideoIdx: index('idx_playlist_video').on(table.playlistId, table.videoId),
}));

export type Video = typeof videos.$inferSelect;
export type NewVideo = typeof videos.$inferInsert;
export type Playlist = typeof playlists.$inferSelect;
export type NewPlaylist = typeof playlists.$inferInsert;
export type PlaylistVideo = typeof playlistVideos.$inferSelect;
export type NewPlaylistVideo = typeof playlistVideos.$inferInsert;
