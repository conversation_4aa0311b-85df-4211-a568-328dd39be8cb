import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as dotenv from 'dotenv';
import * as schemas from '../models/schemas';

dotenv.config();

// Create connection pool
const poolConnection = mysql.createPool({
  host: process.env.MYSQL_HOST!,
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USERNAME!,
  password: process.env.MYSQL_PASSWORD!,
  database: process.env.MYSQL_DB!,
  waitForConnections: true,
  connectionLimit: 50,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 30000,
  reconnect: true,
  charset: 'utf8mb4',
});

// Create log database connection pool
const logPoolConnection = mysql.createPool({
  host: process.env.MYSQL_HOST!,
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USERNAME!,
  password: process.env.MYSQL_PASSWORD!,
  database: 'logs',
  waitForConnections: true,
  connectionLimit: 20,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 30000,
  reconnect: true,
  charset: 'utf8mb4',
});

// Create Drizzle instances
export const db = drizzle(poolConnection, { 
  schema: schemas,
  mode: 'default',
  logger: process.env.NODE_ENV !== 'production'
});

export const logDB = drizzle(logPoolConnection, {
  mode: 'default',
  logger: process.env.NODE_ENV !== 'production'
});

// Connection test functions
export const connectDatabase = async (retries: number = 5): Promise<void> => {
  while (retries > 0) {
    try {
      const connection = await poolConnection.getConnection();
      await connection.ping();
      connection.release();
      console.log('✅ Database connected successfully.');
      return;
    } catch (error) {
      console.error(`❌ Unable to connect to database. Retries left: ${retries}`, error);
      retries -= 1;
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5s before retry
      }
    }
  }
  throw new Error('❌ Database connection failed after multiple retries.');
};

export const connectLogDatabase = async (retries: number = 5): Promise<void> => {
  while (retries > 0) {
    try {
      const connection = await logPoolConnection.getConnection();
      await connection.ping();
      connection.release();
      console.log('✅ Log database connected successfully.');
      return;
    } catch (error) {
      console.error(`❌ Unable to connect to log database. Retries left: ${retries}`, error);
      retries -= 1;
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5s before retry
      }
    }
  }
  throw new Error('❌ Log database connection failed after multiple retries.');
};

// Graceful shutdown
export const closeDatabase = async (): Promise<void> => {
  try {
    await poolConnection.end();
    await logPoolConnection.end();
    console.log('✅ Database connections closed.');
  } catch (error) {
    console.error('❌ Error closing database connections:', error);
  }
};

// Export connection pools for direct access if needed
export { poolConnection, logPoolConnection };
