import { APIError } from './base.error';

export class ValidationError extends APIError {
  public readonly statusCode = 400;
  public readonly isOperational = true;

  constructor(errors: Record<string, string>, message: string = 'Validation failed') {
    super(message);
    this.errors = errors;
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, ValidationError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
      errors: this.errors,
    };
  }
}
