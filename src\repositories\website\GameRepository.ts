import { BaseRepository } from '../base/BaseRepository';
import { <PERSON><PERSON>zleDB, PaginatedResult, PaginationParams } from '../../types/database';
import { games, gameReports, Game } from '../../models/schemas';
import { eq, and, or, like, desc, asc, count, sql } from 'drizzle-orm';

export class GameRepository extends BaseRepository<Game> {
  constructor(db: DrizzleDB) {
    super(db, games);
  }

  // Find game by code
  async findByCode(code: string): Promise<Game | null> {
    try {
      const result = await this.db
        .select()
        .from(games)
        .where(eq(games.code, code))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error(`Error finding game by code ${code}:`, error);
      throw error;
    }
  }

  // Find games by codes in FID
  async findByCodesInFid(codes: string[], fid: string): Promise<Game[]> {
    try {
      const codeConditions = codes.map(code => eq(games.code, code));
      
      return await this.db
        .select()
        .from(games)
        .where(or(...codeConditions)) as Game[];
    } catch (error) {
      console.error('Error finding games by codes:', error);
      throw error;
    }
  }

  // Get top games by FID
  async getTopGamesByFid(
    fid: string,
    options: {
      period: string;
      page: number;
      limit: number;
    }
  ): Promise<PaginatedResult<any>> {
    try {
      const { period, page, limit } = options;
      const offset = (page - 1) * limit;

      // Calculate date range based on period
      const now = new Date();
      let startDate: Date;
      
      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default: // month
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const month = startDate.getMonth() + 1;
      const year = startDate.getFullYear();

      // Get total count
      const totalResult = await this.db
        .select({ count: count() })
        .from(games)
        .innerJoin(gameReports, eq(games.code, gameReports.code))
        .where(
          and(
            eq(gameReports.fid, fid),
            eq(gameReports.month, month),
            eq(gameReports.year, year)
          )
        );

      const totalItems = totalResult[0]?.count || 0;

      // Get items
      const items = await this.db
        .select({
          id: games.id,
          code: games.code,
          name: games.name,
          logo: games.logo,
          totalOpen: gameReports.totalOpen,
        })
        .from(games)
        .innerJoin(gameReports, eq(games.code, gameReports.code))
        .where(
          and(
            eq(gameReports.fid, fid),
            eq(gameReports.month, month),
            eq(gameReports.year, year)
          )
        )
        .orderBy(desc(gameReports.totalOpen))
        .limit(limit)
        .offset(offset);

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
      };
    } catch (error) {
      console.error('Error getting top games by FID:', error);
      throw error;
    }
  }

  // Get user games
  async getUserGames(
    fid: string,
    uid: string,
    options: {
      period: string;
      page: number;
      limit: number;
    }
  ): Promise<PaginatedResult<any>> {
    try {
      const { page, limit } = options;
      const offset = (page - 1) * limit;

      // This would typically join with user_game_reports table
      // For now, return mock paginated data
      const mockItems = [
        { code: 'game1', name: 'Game 1', playCount: 15, lastPlayed: new Date() },
        { code: 'game2', name: 'Game 2', playCount: 8, lastPlayed: new Date() },
      ];

      return {
        items: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(mockItems.length / limit),
      };
    } catch (error) {
      console.error('Error getting user games:', error);
      throw error;
    }
  }

  // Update game status
  async updateStatus(code: string, status: number): Promise<boolean> {
    try {
      const result = await this.db
        .update(games)
        .set({ status, updatedAt: new Date() })
        .where(eq(games.code, code));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error updating game status for ${code}:`, error);
      throw error;
    }
  }

  // Delete game by code
  async deleteByCode(code: string): Promise<boolean> {
    try {
      const result = await this.db
        .delete(games)
        .where(eq(games.code, code));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error deleting game ${code}:`, error);
      throw error;
    }
  }

  // Search games
  async searchGames(
    query: string,
    filters: any,
    pagination: PaginationParams,
    sort: { field: string; direction: 'asc' | 'desc' }
  ): Promise<PaginatedResult<Game>> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      // Build search conditions
      const searchConditions = [
        like(games.name, `%${query}%`),
        like(games.code, `%${query}%`),
      ];

      let whereClause = or(...searchConditions);

      // Add filters
      if (filters.status !== undefined) {
        whereClause = and(whereClause, eq(games.status, filters.status));
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: count() })
        .from(games)
        .where(whereClause);

      const totalItems = totalResult[0]?.count || 0;

      // Get items
      const orderBy = sort.direction === 'desc' 
        ? desc(games[sort.field as keyof typeof games])
        : asc(games[sort.field as keyof typeof games]);

      const items = await this.db
        .select()
        .from(games)
        .where(whereClause)
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset) as Game[];

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
      };
    } catch (error) {
      console.error('Error searching games:', error);
      throw error;
    }
  }

  // Get game statistics
  async getGameStats(filters: any, pagination: PaginationParams): Promise<any> {
    try {
      // This would typically aggregate data from game reports
      // For now, return mock data
      return {
        totalGames: 150,
        activeGames: 120,
        totalPlays: 5420,
        uniquePlayers: 1250,
        topGames: [
          { code: 'game1', name: 'Game 1', plays: 450 },
          { code: 'game2', name: 'Game 2', plays: 380 },
        ],
      };
    } catch (error) {
      console.error('Error getting game stats:', error);
      throw error;
    }
  }

  // Get trending games
  async getTrendingGames(limit: number, period: string): Promise<Game[]> {
    try {
      // This would typically calculate trending based on recent activity
      // For now, return recent games
      return await this.db
        .select()
        .from(games)
        .where(eq(games.status, 1))
        .orderBy(desc(games.createdAt))
        .limit(limit) as Game[];
    } catch (error) {
      console.error('Error getting trending games:', error);
      throw error;
    }
  }

  // Get game analytics
  async getGameAnalytics(filters: any, pagination: PaginationParams): Promise<any> {
    try {
      // This would typically provide detailed analytics
      // For now, return mock analytics data
      return {
        overview: {
          totalGames: 150,
          totalPlays: 5420,
          averageSessionTime: 12.5,
          retentionRate: 0.68,
        },
        trends: [
          { date: '2024-01-01', plays: 120, uniqueUsers: 45 },
          { date: '2024-01-02', plays: 135, uniqueUsers: 52 },
        ],
        topPerformers: [
          { code: 'game1', name: 'Game 1', plays: 450, rating: 4.5 },
          { code: 'game2', name: 'Game 2', plays: 380, rating: 4.2 },
        ],
      };
    } catch (error) {
      console.error('Error getting game analytics:', error);
      throw error;
    }
  }
}
