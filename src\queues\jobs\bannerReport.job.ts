import { Job } from 'bullmq';
import { db } from '../../config/database';
import { bannerReports } from '../../models/schemas';
import { eq, and } from 'drizzle-orm';

// Job data interface
export interface BannerReportJobData {
  bannerId: number;
  fid: string;
  type: number;
  currentDate: string; // YYYY-MM-DD format
}

// Job options
export const bannerReportJobOptions = {
  removeOnComplete: 10,
  removeOnFail: 5,
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
};

// Job processor
export const processBannerReport = async (job: Job<BannerReportJobData>): Promise<void> => {
  const { bannerId, fid, type, currentDate } = job.data;

  try {
    // Parse date to get month and year
    const date = new Date(currentDate);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    console.log(`Processing banner report job: ${bannerId}/${fid}/${type}/${currentDate}`);

    // Check if report already exists
    const existingReport = await db
      .select()
      .from(bannerReports)
      .where(
        and(
          eq(bannerReports.bannerId, bannerId),
          eq(bannerReports.fid, fid),
          eq(bannerReports.type, type),
          eq(bannerReports.month, month),
          eq(bannerReports.year, year)
        )
      )
      .limit(1);

    if (existingReport.length > 0) {
      // Update existing report
      const currentData = JSON.parse(existingReport[0].data);
      
      // Update daily data
      if (!currentData.daily) {
        currentData.daily = {};
      }
      
      if (!currentData.daily[currentDate]) {
        currentData.daily[currentDate] = 0;
      }
      
      currentData.daily[currentDate] += 1;
      
      // Update total
      const newTotal = existingReport[0].totalOpen + 1;

      await db
        .update(bannerReports)
        .set({
          data: JSON.stringify(currentData),
          totalOpen: newTotal,
        })
        .where(
          and(
            eq(bannerReports.bannerId, bannerId),
            eq(bannerReports.fid, fid),
            eq(bannerReports.type, type),
            eq(bannerReports.month, month),
            eq(bannerReports.year, year)
          )
        );

      console.log(`Updated banner report: ${bannerId}/${fid}/${type}/${currentDate} - Total: ${newTotal}`);
    } else {
      // Create new report
      const reportData = {
        daily: {
          [currentDate]: 1,
        },
        metadata: {
          firstClick: currentDate,
          lastClick: currentDate,
        },
      };

      await db.insert(bannerReports).values({
        bannerId,
        fid,
        type,
        month,
        year,
        data: JSON.stringify(reportData),
        totalOpen: 1,
      });

      console.log(`Created new banner report: ${bannerId}/${fid}/${type}/${currentDate}`);
    }

    // Update banner click statistics
    await updateBannerStatistics(bannerId, type, currentDate);

    console.log(`Banner report job completed: ${bannerId}/${fid}/${type}/${currentDate}`);
  } catch (error) {
    console.error(`Banner report job failed: ${bannerId}/${fid}/${type}/${currentDate}`, error);
    throw error;
  }
};

// Helper function to update banner statistics
const updateBannerStatistics = async (bannerId: number, type: number, currentDate: string): Promise<void> => {
  try {
    const { cacheService } = await import('../../config/redis');
    
    // Update daily statistics in cache
    const dailyKey = `banner:stats:daily:${bannerId}:${currentDate}`;
    await cacheService.increment(dailyKey, 86400); // 24 hours TTL

    // Update monthly statistics
    const date = new Date(currentDate);
    const monthKey = `banner:stats:monthly:${bannerId}:${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    await cacheService.increment(monthKey, 86400 * 31); // 31 days TTL

    // Update banner type statistics
    const typeKey = `banner:stats:type:${bannerId}:${type}:${currentDate}`;
    await cacheService.increment(typeKey, 86400);

    // Update real-time statistics
    const realtimeKey = `banner:stats:realtime:${bannerId}`;
    const realtimeData = await cacheService.get(realtimeKey) || {
      totalClicks: 0,
      todayClicks: 0,
      lastClick: null,
    };

    realtimeData.totalClicks += 1;
    realtimeData.todayClicks += 1;
    realtimeData.lastClick = new Date().toISOString();

    await cacheService.set(realtimeKey, realtimeData, 3600); // 1 hour TTL

    console.log(`Updated banner statistics: ${bannerId}/${type}/${currentDate}`);
  } catch (error) {
    console.error(`Failed to update banner statistics: ${bannerId}/${type}/${currentDate}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Job failure handler
export const handleBannerReportJobFailure = async (job: Job<BannerReportJobData>, error: Error): Promise<void> => {
  const { bannerId, fid, type, currentDate } = job.data;
  console.error(`Banner report job failed permanently: ${bannerId}/${fid}/${type}/${currentDate}`, {
    error: error.message,
    stack: error.stack,
    attempts: job.attemptsMade,
    data: job.data,
  });

  // Could send notification or log to external service here
};

// Job completion handler
export const handleBannerReportJobCompletion = async (job: Job<BannerReportJobData>): Promise<void> => {
  const { bannerId, fid, type, currentDate } = job.data;
  console.log(`Banner report job completed successfully: ${bannerId}/${fid}/${type}/${currentDate}`);
  
  // Could update metrics or send notifications here
};
