# Environment Setup Guide

## Overview

This guide explains how to set up environment variables for the FUS CMS application across different environments (development, staging, production).

## Quick Start

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Update the values in `.env`:**
   - Database credentials
   - JWT secret keys
   - Redis configuration
   - API keys and secrets

3. **Validate configuration:**
   ```bash
   npm run env:validate
   ```

## Environment Files

### File Structure

```
.env.example          # Template with all available variables
.env                  # Development environment (gitignored)
.env.local           # Local overrides (gitignored)
.env.production      # Production environment (gitignored)
.env.staging         # Staging environment (gitignored)
.env.test           # Test environment (gitignored)
```

### File Priority

Environment variables are loaded in this order (later files override earlier ones):

1. `.env`
2. `.env.local`
3. `.env.${NODE_ENV}`
4. `.env.${NODE_ENV}.local`
5. System environment variables

## Required Variables

### Critical Settings (Must be changed from defaults)

```env
# Database - REQUIRED
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name

# JWT - REQUIRED (minimum 32 characters)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# API Security - REQUIRED (minimum 32 characters)
API_SECRET_KEY=your-api-secret-key-for-request-signing

# Webhook Security - REQUIRED (minimum 16 characters)
WEBHOOK_SECRET=your-webhook-secret-key
```

### Database Configuration

```env
# Main Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=fus_cms_user
DB_PASSWORD=secure_password_123
DB_NAME=fus_cms
DB_SSL=false
DB_TIMEZONE=+07:00

# Log Database (separate for analytics)
LOG_DB_HOST=localhost
LOG_DB_PORT=3306
LOG_DB_USER=fus_cms_log_user
LOG_DB_PASSWORD=secure_log_password_123
LOG_DB_NAME=fus_cms_logs
LOG_DB_SSL=false

# Alternative: Use connection URLs
DATABASE_URL=mysql://user:password@localhost:3306/fus_cms
LOG_DATABASE_URL=mysql://user:password@localhost:3306/fus_cms_logs
```

### Redis Configuration

```env
# Redis for caching and sessions
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=optional_redis_password
REDIS_DB=0
REDIS_KEY_PREFIX=fus-cms:

# Alternative: Use connection URL
REDIS_URL=redis://localhost:6379
```

## Environment-Specific Configurations

### Development Environment

```env
NODE_ENV=development
DEBUG_ENABLED=true
DEBUG_SQL_QUERIES=true
DEBUG_CACHE_OPERATIONS=true
DEBUG_QUEUE_JOBS=true

# Development tools
DEV_TOOLS_ENABLED=true
API_DOCS_ENABLED=true
QUEUE_DASHBOARD_ENABLED=true

# Relaxed rate limiting
API_RATE_LIMIT_MAX_REQUESTS=200
AUTH_RATE_LIMIT_MAX_REQUESTS=10
```

### Production Environment

```env
NODE_ENV=production
DEBUG_ENABLED=false
DEBUG_SQL_QUERIES=false
DEBUG_CACHE_OPERATIONS=false
DEBUG_QUEUE_JOBS=false

# Production tools
DEV_TOOLS_ENABLED=false
API_DOCS_ENABLED=false
QUEUE_DASHBOARD_ENABLED=false

# Strict rate limiting
API_RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# Enhanced security
DB_SSL=true
CORS_ORIGIN=https://yourdomain.com
```

### Test Environment

```env
NODE_ENV=test
LOG_LEVEL=error

# Test database
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password
TEST_DB_NAME=fus_cms_test

# Disable external services
EMAIL_ENABLED=false
METRICS_ENABLED=false
CLOUD_STORAGE_ENABLED=false
```

## Security Best Practices

### JWT Configuration

```env
# Use a strong, random secret (minimum 64 characters for production)
JWT_SECRET=your-super-long-random-secret-key-with-at-least-64-characters-for-production

# Set appropriate expiration times
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Use specific issuer and audience
JWT_ISSUER=fus-cms
JWT_AUDIENCE=fus-cms-api
```

### Password Security

```env
# Strong password requirements
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
PASSWORD_HISTORY_LIMIT=5
```

### API Security

```env
# Strong API secret for request signing
API_SECRET_KEY=your-api-secret-key-minimum-32-characters-long

# Webhook security
WEBHOOK_SECRET=your-webhook-secret-minimum-16-characters

# Request signature settings
SIGNATURE_ALGORITHM=sha256
SIGNATURE_TIMESTAMP_TOLERANCE=300
```

### CORS Configuration

```env
# Production CORS settings
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com
CORS_CREDENTIALS=true
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-API-Key,X-Signature,X-Timestamp
```

## Rate Limiting Configuration

### Global Rate Limiting

```env
# 1000 requests per 15 minutes per IP
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
```

### Authentication Rate Limiting

```env
# 5 login attempts per 15 minutes per IP/username
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5
AUTH_RATE_LIMIT_SKIP_SUCCESSFUL=true
```

### API Rate Limiting

```env
# 100 API requests per minute per user/IP
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX_REQUESTS=100
```

## Cache Configuration

```env
# Default cache TTL settings (in seconds)
CACHE_TTL_DEFAULT=3600        # 1 hour
CACHE_TTL_SHORT=900           # 15 minutes
CACHE_TTL_LONG=7200           # 2 hours
CACHE_TTL_VERY_LONG=86400     # 24 hours

# Specific cache TTL settings
CACHE_TTL_GAMES_LIST=1800     # 30 minutes
CACHE_TTL_GAMES_DETAIL=3600   # 1 hour
CACHE_TTL_BANNERS_LIST=1800   # 30 minutes
CACHE_TTL_USER_SESSION=900    # 15 minutes
CACHE_TTL_ANALYTICS=1800      # 30 minutes
```

## Queue Configuration

```env
# Queue Redis (separate from main Redis)
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1
QUEUE_REDIS_KEY_PREFIX=fus-cms:bull:

# Job settings
QUEUE_DEFAULT_JOB_ATTEMPTS=3
QUEUE_DEFAULT_JOB_BACKOFF_TYPE=exponential
QUEUE_DEFAULT_JOB_BACKOFF_DELAY=2000
QUEUE_DEFAULT_REMOVE_ON_COMPLETE=10
QUEUE_DEFAULT_REMOVE_ON_FAIL=5

# Worker settings
QUEUE_WORKER_CONCURRENCY=5
```

## Logging Configuration

```env
# Log levels: error, warn, info, debug
LOG_LEVEL=info
LOG_FORMAT=json

# Log files
LOG_FILE_ERROR=logs/error.log
LOG_FILE_COMBINED=logs/combined.log
LOG_FILE_ACCESS=logs/access.log

# Log rotation
LOG_DATE_PATTERN=YYYY-MM-DD-HH
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
```

## Feature Flags

```env
# Enable/disable features
FEATURE_ANALYTICS_ENABLED=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_ADVANCED_CACHING=true
FEATURE_QUEUE_DASHBOARD=true
FEATURE_API_VERSIONING=true
FEATURE_REQUEST_SIGNING=false
FEATURE_RATE_LIMITING=true
FEATURE_AUDIT_LOGGING=true
```

## Validation and Testing

### Validate Environment

```bash
# Check environment configuration
npm run env:validate

# Check specific environment
NODE_ENV=production npm run env:validate
```

### Test Database Connection

```bash
# Test main database
npm run db:test

# Test log database
npm run db:test:logs
```

### Test Redis Connection

```bash
# Test Redis connection
npm run redis:test

# Test queue Redis connection
npm run redis:test:queue
```

## Deployment

### Docker Environment

```dockerfile
# Dockerfile
ENV NODE_ENV=production
ENV PORT=3000

# Copy environment file
COPY .env.production .env
```

### Docker Compose

```yaml
# docker-compose.yml
services:
  api:
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    env_file:
      - .env.production
```

### Kubernetes

```yaml
# k8s-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fus-cms-config
data:
  NODE_ENV: "production"
  PORT: "3000"
  DB_HOST: "mysql-service"
  REDIS_HOST: "redis-service"
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database credentials
   mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME
   ```

2. **Redis Connection Failed**
   ```bash
   # Check Redis connection
   redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
   ```

3. **JWT Token Invalid**
   - Ensure JWT_SECRET is at least 32 characters
   - Check JWT_EXPIRES_IN format (e.g., '24h', '7d')

4. **Rate Limiting Too Strict**
   - Increase rate limit values for development
   - Check Redis for rate limit keys

### Environment Validation Errors

The application validates all environment variables on startup. Common validation errors:

- **Missing required variables**: Set all required environment variables
- **Invalid format**: Check data types (numbers, booleans, URLs)
- **Security warnings**: Use strong secrets and appropriate settings for production

### Debug Mode

Enable debug mode to see detailed configuration:

```env
DEBUG_ENABLED=true
DEBUG_SQL_QUERIES=true
DEBUG_CACHE_OPERATIONS=true
DEBUG_QUEUE_JOBS=true
DEBUG_API_REQUESTS=true
```

## Security Checklist

- [ ] Change all default passwords and secrets
- [ ] Use strong JWT secrets (64+ characters for production)
- [ ] Enable SSL for database connections in production
- [ ] Set appropriate CORS origins
- [ ] Configure rate limiting for your use case
- [ ] Disable debug mode in production
- [ ] Use environment-specific configurations
- [ ] Never commit `.env` files to version control
- [ ] Regularly rotate secrets and API keys
- [ ] Monitor for security vulnerabilities

## Support

For environment setup issues:
- Check the logs: `tail -f logs/error.log`
- Validate configuration: `npm run env:validate`
- Test connections: `npm run test:connections`
- Health check: `curl http://localhost:3000/health-check`
