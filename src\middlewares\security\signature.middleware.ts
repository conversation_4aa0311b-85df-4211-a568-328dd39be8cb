import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { APIRequest, SignatureHeaders } from '../../types/api';
import { UnauthorizedError } from '../../utils/errors/unauthorized.error';
import { BadRequestError } from '../../utils/errors/badRequest.error';

export interface SignatureConfig {
  secret: string;
  algorithm?: string;
  timestampTolerance?: number; // in seconds
  headerPrefix?: string;
  requireTimestamp?: boolean;
  customKeyExtractor?: (req: APIRequest) => string;
}

export class SignatureMiddleware {
  private config: Required<SignatureConfig>;

  constructor(config: SignatureConfig) {
    this.config = {
      algorithm: 'sha256',
      timestampTolerance: 300, // 5 minutes
      headerPrefix: 'x-',
      requireTimestamp: true,
      customKeyExtractor: () => config.secret,
      ...config,
    };
  }

  // Main signature verification middleware
  verifySignature = (req: APIRequest, res: Response, next: NextFunction): void => {
    try {
      // Extract headers
      const timestamp = this.extractTimestamp(req);
      const signature = this.extractSignature(req);

      // Verify timestamp if required
      if (this.config.requireTimestamp) {
        this.verifyTimestamp(timestamp);
      }

      // Get signing key
      const signingKey = this.config.customKeyExtractor(req);

      // Create payload for signature
      const payload = this.createPayload(req, timestamp);

      // Generate expected signature
      const expectedSignature = this.generateSignature(payload, signingKey);

      // Verify signature
      if (!this.compareSignatures(signature, expectedSignature)) {
        throw new UnauthorizedError('Invalid request signature');
      }

      // Store verified data in request
      req.signature = {
        timestamp,
        verified: true,
      };

      next();
    } catch (error) {
      next(error);
    }
  };

  // Create signature for outgoing requests
  static createSignature(
    payload: string,
    secret: string,
    algorithm: string = 'sha256'
  ): string {
    return crypto
      .createHmac(algorithm, secret)
      .update(payload)
      .digest('hex');
  }

  // Create payload string for signing
  private createPayload(req: APIRequest, timestamp?: number): string {
    const method = req.method.toUpperCase();
    const path = req.originalUrl || req.url;
    const body = req.body ? JSON.stringify(req.body) : '';
    const timestampStr = timestamp ? timestamp.toString() : '';

    // Create canonical string
    return [method, path, body, timestampStr].join('\n');
  }

  // Generate HMAC signature
  private generateSignature(payload: string, secret: string): string {
    return crypto
      .createHmac(this.config.algorithm, secret)
      .update(payload)
      .digest('hex');
  }

  // Extract timestamp from headers
  private extractTimestamp(req: APIRequest): number | undefined {
    const timestampHeader = req.headers[`${this.config.headerPrefix}timestamp`] as string;
    
    if (!timestampHeader) {
      if (this.config.requireTimestamp) {
        throw new BadRequestError('Missing timestamp header');
      }
      return undefined;
    }

    const timestamp = parseInt(timestampHeader, 10);
    
    if (isNaN(timestamp)) {
      throw new BadRequestError('Invalid timestamp format');
    }

    return timestamp;
  }

  // Extract signature from headers
  private extractSignature(req: APIRequest): string {
    const signatureHeader = req.headers[`${this.config.headerPrefix}signature`] as string;
    
    if (!signatureHeader) {
      throw new BadRequestError('Missing signature header');
    }

    // Handle different signature formats
    if (signatureHeader.startsWith('sha256=')) {
      return signatureHeader.substring(7);
    }

    return signatureHeader;
  }

  // Verify timestamp is within tolerance
  private verifyTimestamp(timestamp?: number): void {
    if (!timestamp) {
      throw new BadRequestError('Timestamp is required');
    }

    const now = Math.floor(Date.now() / 1000);
    const diff = Math.abs(now - timestamp);

    if (diff > this.config.timestampTolerance) {
      throw new UnauthorizedError('Request timestamp is outside acceptable range');
    }
  }

  // Compare signatures using timing-safe comparison
  private compareSignatures(provided: string, expected: string): boolean {
    if (provided.length !== expected.length) {
      return false;
    }

    try {
      return crypto.timingSafeEqual(
        Buffer.from(provided, 'hex'),
        Buffer.from(expected, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  // Middleware factory for different signature schemes
  static createWebhookVerifier(secret: string, algorithm: string = 'sha256') {
    return (req: APIRequest, res: Response, next: NextFunction): void => {
      try {
        const signature = req.headers['x-signature'] as string;
        
        if (!signature) {
          throw new BadRequestError('Missing webhook signature');
        }

        const body = JSON.stringify(req.body);
        const expectedSignature = SignatureMiddleware.createSignature(body, secret, algorithm);
        
        const providedSignature = signature.startsWith('sha256=') 
          ? signature.substring(7) 
          : signature;

        if (!crypto.timingSafeEqual(
          Buffer.from(providedSignature, 'hex'),
          Buffer.from(expectedSignature, 'hex')
        )) {
          throw new UnauthorizedError('Invalid webhook signature');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // GitHub-style webhook verification
  static createGitHubWebhookVerifier(secret: string) {
    return (req: APIRequest, res: Response, next: NextFunction): void => {
      try {
        const signature = req.headers['x-hub-signature-256'] as string;
        
        if (!signature) {
          throw new BadRequestError('Missing GitHub signature');
        }

        const body = JSON.stringify(req.body);
        const expectedSignature = 'sha256=' + crypto
          .createHmac('sha256', secret)
          .update(body)
          .digest('hex');

        if (!crypto.timingSafeEqual(
          Buffer.from(signature),
          Buffer.from(expectedSignature)
        )) {
          throw new UnauthorizedError('Invalid GitHub webhook signature');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Slack-style webhook verification
  static createSlackWebhookVerifier(signingSecret: string) {
    return (req: APIRequest, res: Response, next: NextFunction): void => {
      try {
        const timestamp = req.headers['x-slack-request-timestamp'] as string;
        const signature = req.headers['x-slack-signature'] as string;

        if (!timestamp || !signature) {
          throw new BadRequestError('Missing Slack signature headers');
        }

        // Check timestamp (should be within 5 minutes)
        const now = Math.floor(Date.now() / 1000);
        if (Math.abs(now - parseInt(timestamp)) > 300) {
          throw new UnauthorizedError('Request timestamp too old');
        }

        const body = JSON.stringify(req.body);
        const baseString = `v0:${timestamp}:${body}`;
        const expectedSignature = 'v0=' + crypto
          .createHmac('sha256', signingSecret)
          .update(baseString)
          .digest('hex');

        if (!crypto.timingSafeEqual(
          Buffer.from(signature),
          Buffer.from(expectedSignature)
        )) {
          throw new UnauthorizedError('Invalid Slack webhook signature');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // API key-based signature verification
  static createAPIKeyVerifier(getSecretByKey: (apiKey: string) => Promise<string | null>) {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const apiKey = req.headers['x-api-key'] as string;
        const signature = req.headers['x-signature'] as string;
        const timestamp = req.headers['x-timestamp'] as string;

        if (!apiKey || !signature || !timestamp) {
          throw new BadRequestError('Missing required headers');
        }

        // Get secret for API key
        const secret = await getSecretByKey(apiKey);
        if (!secret) {
          throw new UnauthorizedError('Invalid API key');
        }

        // Verify timestamp
        const now = Math.floor(Date.now() / 1000);
        if (Math.abs(now - parseInt(timestamp)) > 300) {
          throw new UnauthorizedError('Request timestamp too old');
        }

        // Create payload and verify signature
        const method = req.method.toUpperCase();
        const path = req.originalUrl || req.url;
        const body = req.body ? JSON.stringify(req.body) : '';
        const payload = [method, path, body, timestamp].join('\n');

        const expectedSignature = crypto
          .createHmac('sha256', secret)
          .update(payload)
          .digest('hex');

        if (!crypto.timingSafeEqual(
          Buffer.from(signature, 'hex'),
          Buffer.from(expectedSignature, 'hex')
        )) {
          throw new UnauthorizedError('Invalid request signature');
        }

        // Store API key in request
        req.apiKey = apiKey;

        next();
      } catch (error) {
        next(error);
      }
    };
  }
}
