import { MySql2Database } from 'drizzle-orm/mysql2';
import * as schema from '../../models/schemas';

export type DrizzleDB = MySql2Database<typeof schema>;

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResult<T> {
  items: T[];
  totalItems: number;
  itemsPerPage: number;
  currentPage: number;
  totalPages: number;
}

// Filter types
export interface BaseFilter {
  id?: number;
  status?: number;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  createdBy?: number;
  updatedBy?: number;
}

export interface SoftDeleteFilter extends BaseFilter {
  deletedAt?: Date | string | null;
  deletedBy?: number | null;
}

// Common database operations
export interface BaseRepository<T> {
  findById(id: number): Promise<T | null>;
  findMany(filter?: Partial<T>, pagination?: PaginationParams): Promise<T[]>;
  findWithPagination(filter?: Partial<T>, pagination?: PaginationParams): Promise<PaginatedResult<T>>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: number, data: Partial<T>): Promise<T | null>;
  delete(id: number): Promise<boolean>;
}

// Soft delete repository
export interface SoftDeleteRepository<T> extends BaseRepository<T> {
  softDelete(id: number, deletedBy?: number): Promise<boolean>;
  restore(id: number): Promise<boolean>;
  findWithDeleted(filter?: Partial<T>, pagination?: PaginationParams): Promise<T[]>;
  findOnlyDeleted(filter?: Partial<T>, pagination?: PaginationParams): Promise<T[]>;
}

// Report specific types
export interface ReportFilter {
  fid?: string;
  month?: number;
  year?: number;
  startDate?: Date | string;
  endDate?: Date | string;
}

export interface GameReportFilter extends ReportFilter {
  code?: string;
  uid?: string;
}

export interface BannerReportFilter extends ReportFilter {
  bannerId?: number;
  type?: number;
}

export interface LinkReportFilter extends ReportFilter {
  link?: string;
  type?: number;
}

// Cache types
export interface CacheOptions {
  key: string;
  ttl?: number; // Time to live in seconds
  tags?: string[]; // For cache invalidation
}

// Query builder types
export interface QueryOptions {
  select?: string[];
  include?: string[];
  orderBy?: Array<{ field: string; direction: 'asc' | 'desc' }>;
  groupBy?: string[];
  having?: Record<string, any>;
}

// Transaction types
export type TransactionCallback<T> = (tx: DrizzleDB) => Promise<T>;

// Export all schema types
export * from '../../models/schemas';
