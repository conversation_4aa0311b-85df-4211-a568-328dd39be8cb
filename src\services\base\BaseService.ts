import { BaseRepository } from '../../repositories/base/BaseRepository';
import { CacheService } from '../../config/redis';
import { PaginatedResult, PaginationParams } from '../../types/database';
import { QueueService } from '../../queues';

export abstract class BaseService {
  protected readonly repository: BaseRepository<any>;
  protected readonly cacheService: CacheService;
  protected readonly queueService: typeof QueueService;

  constructor(
    repository: BaseRepository<any>,
    cacheService: CacheService,
    queueService: typeof QueueService = QueueService
  ) {
    this.repository = repository;
    this.cacheService = cacheService;
    this.queueService = queueService;
  }

  // Generic cached data retrieval
  protected async getCachedOrFetch<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    try {
      const cached = await this.cacheService.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      const result = await fetcher();
      await this.cacheService.set(key, result, ttl);
      return result;
    } catch (error) {
      console.error(`Cache operation failed for key ${key}:`, error);
      // Fallback to direct fetch if cache fails
      return await fetcher();
    }
  }

  // Generic cache invalidation
  protected async invalidateCache(pattern: string): Promise<void> {
    try {
      await this.cacheService.invalidatePattern(pattern);
    } catch (error) {
      console.error(`Cache invalidation failed for pattern ${pattern}:`, error);
    }
  }

  // Generic cache key generation
  protected generateCacheKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  // Generic list with caching
  protected async getCachedList<T>(
    cacheKey: string,
    fetcher: () => Promise<T[]>,
    ttl: number = 1800
  ): Promise<T[]> {
    return this.getCachedOrFetch(cacheKey, fetcher, ttl);
  }

  // Generic paginated list with caching
  protected async getCachedPaginatedList<T>(
    cacheKey: string,
    fetcher: () => Promise<PaginatedResult<T>>,
    ttl: number = 1800
  ): Promise<PaginatedResult<T>> {
    return this.getCachedOrFetch(cacheKey, fetcher, ttl);
  }

  // Generic detail with caching
  protected async getCachedDetail<T>(
    cacheKey: string,
    fetcher: () => Promise<T | null>,
    ttl: number = 3600
  ): Promise<T | null> {
    return this.getCachedOrFetch(cacheKey, fetcher, ttl);
  }

  // Generic create with cache invalidation
  protected async createWithCacheInvalidation<T>(
    data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>,
    cachePatterns: string[] = []
  ): Promise<T> {
    const result = await this.repository.create(data);
    
    // Invalidate related caches
    await Promise.all(
      cachePatterns.map(pattern => this.invalidateCache(pattern))
    );

    return result;
  }

  // Generic update with cache invalidation
  protected async updateWithCacheInvalidation<T>(
    id: number,
    data: Partial<T>,
    cachePatterns: string[] = []
  ): Promise<T | null> {
    const result = await this.repository.update(id, data);
    
    if (result) {
      // Invalidate related caches
      await Promise.all(
        cachePatterns.map(pattern => this.invalidateCache(pattern))
      );
    }

    return result;
  }

  // Generic delete with cache invalidation
  protected async deleteWithCacheInvalidation(
    id: number,
    cachePatterns: string[] = []
  ): Promise<boolean> {
    const result = await this.repository.delete(id);
    
    if (result) {
      // Invalidate related caches
      await Promise.all(
        cachePatterns.map(pattern => this.invalidateCache(pattern))
      );
    }

    return result;
  }

  // Bulk operations with cache invalidation
  protected async bulkOperationWithCacheInvalidation<T>(
    items: T[],
    operation: (item: T) => Promise<any>,
    cachePatterns: string[] = []
  ): Promise<{
    successful: T[];
    failed: Array<{ item: T; error: string }>;
    total: number;
  }> {
    const successful: T[] = [];
    const failed: Array<{ item: T; error: string }> = [];

    for (const item of items) {
      try {
        await operation(item);
        successful.push(item);
      } catch (error) {
        failed.push({
          item,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Invalidate caches if any operations were successful
    if (successful.length > 0) {
      await Promise.all(
        cachePatterns.map(pattern => this.invalidateCache(pattern))
      );
    }

    return {
      successful,
      failed,
      total: items.length,
    };
  }

  // Queue job helper
  protected async queueJob<T>(
    queueMethod: (data: T, options?: any) => Promise<any>,
    data: T,
    options?: any
  ): Promise<void> {
    try {
      await queueMethod(data, options);
    } catch (error) {
      console.error('Failed to queue job:', error);
      // Could implement fallback logic here
    }
  }

  // Batch queue jobs
  protected async queueBatchJobs<T>(
    queueMethod: (jobs: Array<{ data: T; options?: any }>) => Promise<any>,
    jobs: Array<{ data: T; options?: any }>
  ): Promise<void> {
    try {
      await queueMethod(jobs);
    } catch (error) {
      console.error('Failed to queue batch jobs:', error);
      // Could implement fallback logic here
    }
  }

  // Data transformation helpers
  protected transformForResponse<T, R>(
    data: T,
    transformer: (item: T) => R
  ): R {
    return transformer(data);
  }

  protected transformListForResponse<T, R>(
    data: T[],
    transformer: (item: T) => R
  ): R[] {
    return data.map(transformer);
  }

  protected transformPaginatedForResponse<T, R>(
    data: PaginatedResult<T>,
    transformer: (item: T) => R
  ): PaginatedResult<R> {
    return {
      ...data,
      items: data.items.map(transformer),
    };
  }

  // Validation helpers
  protected validateData<T>(
    data: any,
    validator: (data: any) => data is T
  ): T {
    if (!validator(data)) {
      throw new Error('Invalid data format');
    }
    return data;
  }

  // Error handling helpers
  protected handleServiceError(error: any, context: string): never {
    console.error(`Service error in ${context}:`, error);
    
    if (error.name === 'ValidationError') {
      throw error;
    }
    
    if (error.name === 'NotFoundError') {
      throw error;
    }
    
    // Generic service error
    throw new Error(`Service operation failed: ${error.message || 'Unknown error'}`);
  }

  // Logging helpers
  protected logOperation(operation: string, data?: any): void {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[${new Date().toISOString()}] Service operation: ${operation}`, data);
    }
  }

  // Performance monitoring
  protected async measurePerformance<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      if (duration > 1000) { // Log slow operations
        console.warn(`Slow operation detected: ${operation} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Operation failed: ${operation} took ${duration}ms`, error);
      throw error;
    }
  }
}
