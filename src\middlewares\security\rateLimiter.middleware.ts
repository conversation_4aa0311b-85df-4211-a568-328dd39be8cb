import { Request, Response, NextFunction } from 'express';
import { APIRequest } from '../../types/api';
import { CacheService } from '../../config/redis';
import { TooManyRequestsError } from '../../utils/errors/tooManyRequests.error';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: APIRequest) => string; // Custom key generator
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  message?: string; // Custom error message
  headers?: boolean; // Include rate limit headers in response
  onLimitReached?: (req: APIRequest, res: Response) => void; // Callback when limit is reached
}

export class RateLimiterMiddleware {
  private cacheService: CacheService;

  constructor(cacheService: CacheService) {
    this.cacheService = cacheService;
  }

  // Create rate limiter middleware
  createRateLimiter(config: RateLimitConfig) {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const key = this.generateKey(req, config.keyGenerator);
        const windowStart = Math.floor(Date.now() / config.windowMs) * config.windowMs;
        const cacheKey = `rate_limit:${key}:${windowStart}`;

        // Get current request count
        const currentCount = await this.cacheService.get<number>(cacheKey) || 0;

        // Check if limit exceeded
        if (currentCount >= config.maxRequests) {
          // Add rate limit headers
          if (config.headers !== false) {
            this.addRateLimitHeaders(res, config, currentCount, windowStart);
          }

          // Call callback if provided
          if (config.onLimitReached) {
            config.onLimitReached(req, res);
          }

          throw new TooManyRequestsError(
            config.message || 'Too many requests, please try again later'
          );
        }

        // Increment counter
        const newCount = currentCount + 1;
        const ttl = Math.ceil((windowStart + config.windowMs - Date.now()) / 1000);
        await this.cacheService.set(cacheKey, newCount, ttl);

        // Add rate limit headers
        if (config.headers !== false) {
          this.addRateLimitHeaders(res, config, newCount, windowStart);
        }

        // Store rate limit info for potential cleanup
        res.locals.rateLimitKey = cacheKey;
        res.locals.rateLimitConfig = config;

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Predefined rate limiters
  static createGlobalLimiter(cacheService: CacheService): (req: APIRequest, res: Response, next: NextFunction) => Promise<void> {
    const limiter = new RateLimiterMiddleware(cacheService);
    return limiter.createRateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 1000, // 1000 requests per 15 minutes
      message: 'Too many requests from this IP, please try again later',
    });
  }

  static createAuthLimiter(cacheService: CacheService): (req: APIRequest, res: Response, next: NextFunction) => Promise<void> {
    const limiter = new RateLimiterMiddleware(cacheService);
    return limiter.createRateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // 5 login attempts per 15 minutes
      keyGenerator: (req) => `auth:${req.ip}:${req.body?.username || req.body?.email || 'unknown'}`,
      message: 'Too many authentication attempts, please try again later',
      skipSuccessfulRequests: true, // Only count failed attempts
    });
  }

  static createAPILimiter(cacheService: CacheService): (req: APIRequest, res: Response, next: NextFunction) => Promise<void> {
    const limiter = new RateLimiterMiddleware(cacheService);
    return limiter.createRateLimiter({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      keyGenerator: (req) => req.user ? `api:user:${req.user.id}` : `api:ip:${req.ip}`,
      message: 'API rate limit exceeded, please slow down',
    });
  }

  static createUploadLimiter(cacheService: CacheService): (req: APIRequest, res: Response, next: NextFunction) => Promise<void> {
    const limiter = new RateLimiterMiddleware(cacheService);
    return limiter.createRateLimiter({
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 10, // 10 uploads per hour
      keyGenerator: (req) => req.user ? `upload:user:${req.user.id}` : `upload:ip:${req.ip}`,
      message: 'Upload rate limit exceeded, please try again later',
    });
  }

  static createPasswordResetLimiter(cacheService: CacheService): (req: APIRequest, res: Response, next: NextFunction) => Promise<void> {
    const limiter = new RateLimiterMiddleware(cacheService);
    return limiter.createRateLimiter({
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // 3 password reset attempts per hour
      keyGenerator: (req) => `password_reset:${req.ip}:${req.body?.email || 'unknown'}`,
      message: 'Too many password reset attempts, please try again later',
    });
  }

  // Sliding window rate limiter (more accurate but uses more memory)
  createSlidingWindowLimiter(config: RateLimitConfig) {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const key = this.generateKey(req, config.keyGenerator);
        const now = Date.now();
        const windowStart = now - config.windowMs;
        const cacheKey = `sliding_rate_limit:${key}`;

        // Get request timestamps
        const timestamps = await this.cacheService.get<number[]>(cacheKey) || [];
        
        // Remove old timestamps
        const validTimestamps = timestamps.filter(timestamp => timestamp > windowStart);

        // Check if limit exceeded
        if (validTimestamps.length >= config.maxRequests) {
          // Add rate limit headers
          if (config.headers !== false) {
            this.addSlidingWindowHeaders(res, config, validTimestamps, now);
          }

          throw new TooManyRequestsError(
            config.message || 'Too many requests, please try again later'
          );
        }

        // Add current timestamp
        validTimestamps.push(now);
        
        // Store updated timestamps
        await this.cacheService.set(cacheKey, validTimestamps, Math.ceil(config.windowMs / 1000));

        // Add rate limit headers
        if (config.headers !== false) {
          this.addSlidingWindowHeaders(res, config, validTimestamps, now);
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Distributed rate limiter (for multiple server instances)
  createDistributedLimiter(config: RateLimitConfig) {
    return async (req: APIRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const key = this.generateKey(req, config.keyGenerator);
        const windowStart = Math.floor(Date.now() / config.windowMs) * config.windowMs;
        const cacheKey = `distributed_rate_limit:${key}:${windowStart}`;

        // Use atomic increment to handle concurrent requests
        const currentCount = await this.cacheService.increment(cacheKey, Math.ceil(config.windowMs / 1000));

        // Check if limit exceeded
        if (currentCount > config.maxRequests) {
          // Add rate limit headers
          if (config.headers !== false) {
            this.addRateLimitHeaders(res, config, currentCount, windowStart);
          }

          throw new TooManyRequestsError(
            config.message || 'Too many requests, please try again later'
          );
        }

        // Add rate limit headers
        if (config.headers !== false) {
          this.addRateLimitHeaders(res, config, currentCount, windowStart);
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Generate cache key
  private generateKey(req: APIRequest, keyGenerator?: (req: APIRequest) => string): string {
    if (keyGenerator) {
      return keyGenerator(req);
    }

    // Default key generation
    if (req.user) {
      return `user:${req.user.id}`;
    }

    return `ip:${req.ip}`;
  }

  // Add rate limit headers to response
  private addRateLimitHeaders(
    res: Response,
    config: RateLimitConfig,
    currentCount: number,
    windowStart: number
  ): void {
    const remaining = Math.max(0, config.maxRequests - currentCount);
    const resetTime = Math.ceil((windowStart + config.windowMs) / 1000);

    res.set({
      'X-RateLimit-Limit': config.maxRequests.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': resetTime.toString(),
      'X-RateLimit-Window': config.windowMs.toString(),
    });

    if (remaining === 0) {
      res.set('Retry-After', Math.ceil((windowStart + config.windowMs - Date.now()) / 1000).toString());
    }
  }

  // Add sliding window headers
  private addSlidingWindowHeaders(
    res: Response,
    config: RateLimitConfig,
    timestamps: number[],
    now: number
  ): void {
    const remaining = Math.max(0, config.maxRequests - timestamps.length);
    const oldestTimestamp = timestamps.length > 0 ? Math.min(...timestamps) : now;
    const resetTime = Math.ceil((oldestTimestamp + config.windowMs) / 1000);

    res.set({
      'X-RateLimit-Limit': config.maxRequests.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': resetTime.toString(),
      'X-RateLimit-Window': config.windowMs.toString(),
    });

    if (remaining === 0) {
      res.set('Retry-After', Math.ceil((oldestTimestamp + config.windowMs - now) / 1000).toString());
    }
  }

  // Clean up rate limit data after response (for failed requests)
  static createCleanupMiddleware() {
    return (req: APIRequest, res: Response, next: NextFunction): void => {
      const originalSend = res.send;
      
      res.send = function(body: any) {
        const statusCode = res.statusCode;
        const rateLimitKey = res.locals.rateLimitKey;
        const rateLimitConfig = res.locals.rateLimitConfig as RateLimitConfig;

        // Clean up counter for failed requests if configured
        if (rateLimitKey && rateLimitConfig) {
          if (
            (rateLimitConfig.skipSuccessfulRequests && statusCode < 400) ||
            (rateLimitConfig.skipFailedRequests && statusCode >= 400)
          ) {
            // Decrement counter (fire and forget)
            // This is a simplified approach - in production you might want more sophisticated cleanup
          }
        }

        return originalSend.call(this, body);
      };

      next();
    };
  }
}
