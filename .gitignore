# =============================================================================
# FUS CMS .gitignore
# =============================================================================

# =============================================================================
# Environment Variables
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# =============================================================================
# Dependencies
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (keep only one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# =============================================================================
# Build Output
# =============================================================================
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/

# =============================================================================
# TypeScript
# =============================================================================
*.tsbuildinfo
.tscache/

# =============================================================================
# Logs
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# =============================================================================
# Coverage and Testing
# =============================================================================
coverage/
*.lcov
.nyc_output/
.coverage/
.vitest/
test-results/
playwright-report/
test-results.xml

# =============================================================================
# IDE and Editor Files
# =============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System Files
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Database Files
# =============================================================================
*.sqlite
*.sqlite3
*.db
*.db-journal
*.db-wal
*.db-shm

# Database dumps
*.sql
*.dump
*.backup

# =============================================================================
# Cache and Temporary Files
# =============================================================================
.cache/
.temp/
.tmp/
temp/
tmp/
*.tmp
*.temp

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# =============================================================================
# Upload and Media Files
# =============================================================================
uploads/
media/
public/uploads/
storage/
files/

# =============================================================================
# SSL Certificates
# =============================================================================
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx
ssl/
certs/

# =============================================================================
# Backup Files
# =============================================================================
backups/
*.backup
*.bak
*.old
*.orig

# =============================================================================
# Docker
# =============================================================================
.dockerignore
docker-compose.override.yml
.docker/

# =============================================================================
# Kubernetes
# =============================================================================
*.kubeconfig
k8s-secrets/

# =============================================================================
# Terraform
# =============================================================================
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =============================================================================
# AWS
# =============================================================================
.aws/
aws-exports.js

# =============================================================================
# Google Cloud
# =============================================================================
.gcloud/
gcloud-service-key.json

# =============================================================================
# Monitoring and Analytics
# =============================================================================
.sentry/
newrelic_agent.log

# =============================================================================
# Package Managers
# =============================================================================
.pnp.*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.js

# =============================================================================
# Runtime and Process Files
# =============================================================================
.pm2/
ecosystem.config.js
pm2.config.js

# =============================================================================
# Documentation
# =============================================================================
# Uncomment if you don't want to track generated docs
# docs/generated/
# api-docs/

# =============================================================================
# Custom Project Files
# =============================================================================
# Add your custom ignore patterns here
.local/
.private/
.secret/
config/local.json
config/production.json

# Development tools
.vite/
.rollup.cache/
.parcel-cache/

# Storybook
storybook-static/

# =============================================================================
# Security Files
# =============================================================================
# Never commit these files
*.env
.env.*
secrets.json
private-key.json
service-account.json
auth-config.json

# API keys and tokens
api-keys.txt
tokens.txt
credentials.json

# =============================================================================
# Miscellaneous
# =============================================================================
*.tgz
*.tar.gz
*.zip
*.rar
*.7z

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.parcel-cache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
.tmp/
.temp/
