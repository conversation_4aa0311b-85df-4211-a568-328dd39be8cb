import { BaseRepository } from '../base/BaseRepository';
import { Dr<PERSON>zleDB } from '../../types/database';
import { fusGames, FusGame } from '../../models/schemas';
import { eq, like, desc, count, gt } from 'drizzle-orm';

export class FusGameRepository extends BaseRepository<FusGame> {
  constructor(db: DrizzleDB) {
    super(db, fusGames);
  }

  // Find FUS game by FID
  async findByFid(fid: string): Promise<FusGame | null> {
    try {
      const result = await this.db
        .select()
        .from(fusGames)
        .where(eq(fusGames.fid, fid))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error(`Error finding FUS game by FID ${fid}:`, error);
      throw error;
    }
  }

  // Create or update FUS game
  async createOrUpdate(data: {
    fid: string;
    data: Array<{ code: string; name: string }>;
  }): Promise<FusGame> {
    try {
      const existing = await this.findByFid(data.fid);

      if (existing) {
        // Update existing
        await this.db
          .update(fusGames)
          .set({
            data: data.data,
            updatedAt: new Date(),
          })
          .where(eq(fusGames.fid, data.fid));

        return await this.findByFid(data.fid) as FusGame;
      } else {
        // Create new
        const result = await this.db.insert(fusGames).values({
          fid: data.fid,
          data: data.data,
          createdAt: new Date(),
        });

        return await this.findById(result[0].insertId) as FusGame;
      }
    } catch (error) {
      console.error('Error creating or updating FUS game:', error);
      throw error;
    }
  }

  // Get FUS game detail with parsed data
  async getFusGameDetail(fid: string): Promise<{
    fid: string;
    data: any;
  } | null> {
    try {
      const fusGame = await this.findByFid(fid);

      if (!fusGame) {
        return null;
      }

      return {
        fid: fusGame.fid,
        data: fusGame.data,
      };
    } catch (error) {
      console.error(`Error getting FUS game detail for FID ${fid}:`, error);
      throw error;
    }
  }

  // Update FUS game data
  async updateGameData(
    fid: string,
    gameData: Array<{ code: string; name: string }>
  ): Promise<boolean> {
    try {
      const result = await this.db
        .update(fusGames)
        .set({
          data: JSON.stringify(gameData),
          updatedAt: new Date(),
        })
        .where(eq(fusGames.fid, fid));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error updating FUS game data for FID ${fid}:`, error);
      throw error;
    }
  }

  // Add games to existing FUS game
  async addGamesToFusGame(
    fid: string,
    newGames: Array<{ code: string; name: string }>
  ): Promise<boolean> {
    try {
      const existing = await this.getFusGameDetail(fid);
      
      if (!existing) {
        // Create new FUS game if doesn't exist
        await this.createOrUpdate({ fid, data: newGames });
        return true;
      }

      // Merge with existing games
      const existingGames = Array.isArray(existing.data) ? existing.data : [];
      const existingCodes = existingGames.map((g: any) => g.code);
      
      // Filter out games that already exist
      const gamesToAdd = newGames.filter(game => !existingCodes.includes(game.code));
      
      if (gamesToAdd.length === 0) {
        return false; // No new games to add
      }

      const updatedGames = [...existingGames, ...gamesToAdd];
      
      return await this.updateGameData(fid, updatedGames);
    } catch (error) {
      console.error(`Error adding games to FUS game for FID ${fid}:`, error);
      throw error;
    }
  }

  // Remove games from FUS game
  async removeGamesFromFusGame(
    fid: string,
    gameCodes: string[]
  ): Promise<boolean> {
    try {
      const existing = await this.getFusGameDetail(fid);
      
      if (!existing) {
        return false;
      }

      const existingGames = Array.isArray(existing.data) ? existing.data : [];
      const updatedGames = existingGames.filter((game: any) => 
        !gameCodes.includes(game.code)
      );

      return await this.updateGameData(fid, updatedGames);
    } catch (error) {
      console.error(`Error removing games from FUS game for FID ${fid}:`, error);
      throw error;
    }
  }

  // Get all FUS games with pagination
  async getAllFusGames(
    page: number = 1,
    limit: number = 10
  ): Promise<{
    items: Array<{ fid: string; data: any; createdAt: Date; updatedAt?: Date }>;
    totalItems: number;
    itemsPerPage: number;
    currentPage: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;

      // Get total count
      const totalResult = await this.db
        .select({ count: this.db.$count() })
        .from(fusGames);

      const totalItems = totalResult[0]?.count || 0;

      // Get items
      const items = await this.db
        .select()
        .from(fusGames)
        .orderBy(this.buildOrderBy('createdAt', 'desc'))
        .limit(limit)
        .offset(offset);

      const formattedItems = items.map(item => ({
        fid: item.fid,
        data: typeof item.data === 'string' ? JSON.parse(item.data) : item.data,
        createdAt: item.createdAt!,
        updatedAt: item.updatedAt,
      }));

      return {
        items: formattedItems,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
      };
    } catch (error) {
      console.error('Error getting all FUS games:', error);
      throw error;
    }
  }

  // Search FUS games by FID pattern
  async searchFusGamesByFid(fidPattern: string): Promise<FusGame[]> {
    try {
      return await this.db
        .select()
        .from(fusGames)
        .where(this.db.like(fusGames.fid, `%${fidPattern}%`))
        .orderBy(this.buildOrderBy('createdAt', 'desc')) as FusGame[];
    } catch (error) {
      console.error(`Error searching FUS games by FID pattern ${fidPattern}:`, error);
      throw error;
    }
  }

  // Get FUS game statistics
  async getFusGameStats(): Promise<{
    totalFusGames: number;
    totalGamesInFus: number;
    averageGamesPerFus: number;
    recentlyUpdated: number;
  }> {
    try {
      // Get total FUS games
      const totalResult = await this.db
        .select({ count: this.db.$count() })
        .from(fusGames);

      const totalFusGames = totalResult[0]?.count || 0;

      // Get all FUS games to calculate game counts
      const allFusGames = await this.db.select().from(fusGames);
      
      let totalGamesInFus = 0;
      let recentlyUpdated = 0;
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      allFusGames.forEach(fusGame => {
        const gameData = typeof fusGame.data === 'string' 
          ? JSON.parse(fusGame.data) 
          : fusGame.data;
        
        if (Array.isArray(gameData)) {
          totalGamesInFus += gameData.length;
        }

        if (fusGame.updatedAt && fusGame.updatedAt > oneDayAgo) {
          recentlyUpdated++;
        }
      });

      const averageGamesPerFus = totalFusGames > 0 
        ? Math.round((totalGamesInFus / totalFusGames) * 100) / 100 
        : 0;

      return {
        totalFusGames,
        totalGamesInFus,
        averageGamesPerFus,
        recentlyUpdated,
      };
    } catch (error) {
      console.error('Error getting FUS game stats:', error);
      throw error;
    }
  }
}
