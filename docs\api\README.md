# FUS CMS API Documentation

## Overview

FUS CMS API is a RESTful API built with Node.js, Express, and TypeScript. It provides endpoints for managing games, banners, videos, and tracking analytics.

## Architecture

### Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MySQL with Drizzle ORM
- **Cache**: Redis
- **Queue**: BullMQ
- **Validation**: Zod
- **Authentication**: JWT
- **Testing**: Vitest

### Project Structure

```
src/
├── config/           # Configuration files
├── controllers/      # Request handlers
├── services/         # Business logic
├── repositories/     # Data access layer
├── models/          # Database schemas
├── middlewares/     # Express middlewares
├── validators/      # Request validation schemas
├── queues/          # Background job processing
├── utils/           # Utility functions
└── types/           # TypeScript type definitions
```

## Authentication

### JWT Authentication

All protected endpoints require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

### Getting a Token

```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

Response:
```json
{
  "status": "success",
  "code": 200,
  "message": "Login successful",
  "metadata": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "your_username",
      "email": "<EMAIL>",
      "roles": ["user"],
      "permissions": ["games:read", "games:write"]
    }
  }
}
```

### Token Refresh

```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token"
}
```

## API Endpoints

### Games API

#### Get Games List

```http
GET /api/v1/game
Authorization: Bearer <token>
```

Query Parameters:
- `fid` (optional): Filter by FID
- `category` (optional): Filter by category
- `status` (optional): Filter by status (0=inactive, 1=active)
- `search` (optional): Search by name or code
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)

Response:
```json
{
  "status": "success",
  "code": 200,
  "message": "Games retrieved successfully",
  "metadata": [
    {
      "id": 1,
      "code": "game1",
      "name": "Game 1",
      "logo": "https://example.com/logo.png",
      "status": 1,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Create Game

```http
POST /api/v1/game
Authorization: Bearer <token>
Content-Type: application/json

{
  "fid": "your_fid",
  "data": [
    {
      "code": "game1",
      "name": "Game 1"
    },
    {
      "code": "game2",
      "name": "Game 2"
    }
  ]
}
```

#### Get Game Detail

```http
GET /api/v1/game/{code}
Authorization: Bearer <token>
```

#### Open Game

```http
POST /api/v1/game/open
Authorization: Bearer <token>
Content-Type: application/json

{
  "code": "game1",
  "fid": "your_fid",
  "uid": "user123",
  "metadata": {
    "source": "web"
  }
}
```

### Banners API

#### Get Banners List

```http
GET /api/v1/banner
Authorization: Bearer <token>
```

Query Parameters:
- `fid` (optional): Filter by FID
- `positionId` (optional): Filter by position ID
- `status` (optional): Filter by status
- `page` (optional): Page number
- `limit` (optional): Items per page

#### Get Banners V2 List

```http
GET /api/v1/banner-v2
Authorization: Bearer <token>
```

Query Parameters:
- `region` (optional): Filter by region
- `city` (optional): Filter by city
- `fid` (optional): Filter by FID
- `positionId` (optional): Filter by position ID
- `clientId` (optional): Filter by client ID
- `active` (optional): Filter by active status (0=inactive, 1=active)

### Tracking API

#### Track Link Click

```http
POST /api/v1/tracking/link
Content-Type: application/json

{
  "link": "https://example.com",
  "type": 1,
  "fid": "your_fid",
  "metadata": {
    "source": "banner"
  }
}
```

#### Track Banner Click

```http
POST /api/v1/tracking/banner
Content-Type: application/json

{
  "bannerId": 123,
  "fid": "your_fid",
  "type": 1
}
```

#### Track Video Interaction

```http
POST /api/v1/tracking/video
Content-Type: application/json

{
  "videoId": 456,
  "fid": "your_fid",
  "playlistId": 789,
  "type": 1,
  "duration": 120,
  "position": 60
}
```

## Error Handling

### Error Response Format

All errors follow a consistent format:

```json
{
  "status": "error",
  "code": 400,
  "message": "Error description",
  "errors": {
    "field1": "Field-specific error message",
    "field2": "Another field error"
  }
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

### Default Limits

- **Global**: 1000 requests per 15 minutes per IP
- **Authentication**: 5 attempts per 15 minutes per IP/username
- **API**: 100 requests per minute per user/IP
- **Upload**: 10 uploads per hour per user/IP
- **Password Reset**: 3 attempts per hour per IP/email

### Rate Limit Headers

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 60000
Retry-After: 60
```

## Validation

### Request Validation

All requests are validated using Zod schemas. Validation errors return detailed field-level error messages.

Example validation error:
```json
{
  "status": "error",
  "code": 400,
  "message": "Validation failed",
  "errors": {
    "fid": "FID is required",
    "data.0.code": "Game code can only contain letters, numbers, underscores, and hyphens",
    "data.0.name": "Game name is required"
  }
}
```

## Security

### Request Signing

For high-security endpoints, requests must be signed with HMAC-SHA256:

```
X-Timestamp: 1640995200
X-Signature: sha256=abc123...
```

### Password Requirements

- Minimum 8 characters
- At least one lowercase letter
- At least one uppercase letter
- At least one number
- At least one special character
- Cannot be a common password

## Pagination

### Request Parameters

```
?page=1&limit=10
```

### Response Format

```json
{
  "status": "success",
  "code": 200,
  "message": "Data retrieved successfully",
  "metadata": {
    "items": [...],
    "totalItems": 100,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 10
  }
}
```

## Caching

### Cache Headers

```
Cache-Control: public, max-age=3600
ETag: "abc123"
Last-Modified: Wed, 21 Oct 2015 07:28:00 GMT
```

### Cache Invalidation

Caches are automatically invalidated when related data is modified.

## Background Jobs

### Queue System

The API uses BullMQ for background job processing:

- **Game Reports**: Update game statistics
- **Banner Reports**: Update banner click statistics
- **Link Reports**: Update link click statistics
- **Event Logging**: Log user events
- **Location Updates**: Update location data

### Job Monitoring

Access the queue dashboard at `/admin/queues` (requires admin privileges).

## Development

### Environment Variables

```env
NODE_ENV=development
PORT=3000
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
DATABASE_URL=mysql://user:password@localhost:3306/database
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

### Running Tests

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# All tests
npm test

# Test coverage
npm run test:coverage
```

### API Testing

Use the provided Postman collection or curl examples:

```bash
# Get games list
curl -H "Authorization: Bearer <token>" \
     http://localhost:3000/api/v1/game

# Create game
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"fid":"test","data":[{"code":"game1","name":"Game 1"}]}' \
     http://localhost:3000/api/v1/game
```

## Support

For API support and questions:
- Documentation: `/api/docs`
- Health Check: `/health-check`
- Queue Dashboard: `/admin/queues`
