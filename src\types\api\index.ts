import { Request, Response, NextFunction } from 'express';

// Base API types
export interface APIResponse<T = any> {
  status: 'success' | 'error';
  code: number;
  message: string;
  metadata?: T;
  errors?: Record<string, string>;
}

export interface APIRequest extends Request {
  api_version?: number;
  user?: AuthUser;
  client?: ClientInfo;
}

export interface AuthUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  roles: string[];
  permissions: string[];
}

export interface ClientInfo {
  id: string;
  name: string;
  version: string;
  signature: string;
  timestamp: number;
}

// Controller types
export type ControllerMethod = (req: APIRequest, res: Response, next: NextFunction) => Promise<void>;

export interface BaseController {
  [key: string]: ControllerMethod;
}

// Middleware types
export type MiddlewareFunction = (req: APIRequest, res: Response, next: NextFunction) => void | Promise<void>;

export interface ValidationMiddleware {
  validate: MiddlewareFunction;
}

export interface AuthMiddleware {
  authenticate: MiddlewareFunction;
  authorize: (permissions: string[]) => MiddlewareFunction;
}

// Service types
export interface BaseService {
  [key: string]: (...args: any[]) => Promise<any>;
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  data?: any;
}

// Game API types
export interface GameListParams {
  fid?: string;
  search?: string;
  category?: string;
  status?: number;
  page?: number;
  limit?: number;
}

export interface GameDetailParams {
  code: string;
}

export interface CreateGameRequest {
  fid: string;
  data: Array<{
    code: string;
    name: string;
  }>;
}

export interface OpenGameRequest {
  code: string;
  fid: string;
  uid?: string;
}

// Banner API types
export interface BannerListParams {
  fid?: string;
  positionId?: number;
  status?: number;
  page?: number;
  limit?: number;
}

// Video API types
export interface VideoListParams {
  playlistSlug?: string;
  search?: string;
  type?: number;
  year?: number;
  status?: number;
  page?: number;
  limit?: number;
  offset?: number;
}

// News API types
export interface NewsListParams {
  category?: string;
  search?: string;
  status?: number;
  page?: number;
  limit?: number;
}

// Tracking API types
export interface TrackLinkRequest {
  link: string;
  type: number;
  fid?: string;
}

export interface TrackBannerRequest {
  bannerId: number;
  fid?: string;
}

export interface TrackVideoRequest {
  videoId: number;
  fid?: string;
  playlistId?: number;
}

// Settings API types
export interface SettingsParams {
  fid: string;
}

export interface SettingItem {
  key: string;
  value: any;
  name: string;
  type: string;
}

// Location API types
export interface LocationParams {
  fid?: string;
  regionId?: number;
  cityId?: number;
}

// RSS API types
export interface RSSParams {
  link: string;
}

// Health check types
export interface HealthCheckResponse {
  status: 'success' | 'error';
  message: string;
  timestamp: string;
  services: {
    database: 'connected' | 'disconnected';
    redis: 'connected' | 'disconnected';
    queue: 'connected' | 'disconnected';
  };
}

// Error types
export interface APIError extends Error {
  statusCode: number;
  isOperational: boolean;
  errors?: Record<string, string>;
}

// Queue job types
export interface JobData {
  [key: string]: any;
}

export interface JobOptions {
  delay?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
  removeOnComplete?: number | boolean;
  removeOnFail?: number | boolean;
}

// Cache types
export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  invalidatePattern(pattern: string): Promise<void>;
}

// File upload types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

export interface FileUploadOptions {
  maxSize?: number;
  allowedMimeTypes?: string[];
  destination?: string;
}

// Rate limiting types
export interface RateLimitOptions {
  windowMs: number;
  max: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
}

// CORS types
export interface CORSOptions {
  origin: string | string[] | RegExp | RegExp[];
  credentials?: boolean;
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  maxAge?: number;
}

// JWT types
export interface JWTPayload {
  sub: number; // subject (user id)
  username: string;
  email: string;
  roles: string[];
  iat?: number; // issued at
  exp?: number; // expiration time
}

export interface JWTOptions {
  secret: string;
  expiresIn: string | number;
  issuer?: string;
  audience?: string;
}

// Signature verification types
export interface SignatureHeaders {
  'x-timestamp': string;
  'x-signature': string;
}

export interface SignaturePayload {
  timestamp: number;
  data: any;
}

// Logging types
export interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp: string;
  requestId?: string;
  userId?: number;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: Error;
  metadata?: Record<string, any>;
}
