import { Job } from 'bullmq';
import { db } from '../../config/database';
import { linkReports } from '../../models/schemas';
import { eq, and } from 'drizzle-orm';

// Job data interface
export interface LinkReportJobData {
  link: string;
  type: number;
  fid: string;
  month: number;
  year: number;
}

// Job options
export const linkReportJobOptions = {
  removeOnComplete: 10,
  removeOnFail: 5,
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
};

// Job processor
export const processLinkReport = async (job: Job<LinkReportJobData>): Promise<void> => {
  const { link, type, fid, month, year } = job.data;

  try {
    console.log(`Processing link report job: ${link}/${type}/${fid}/${month}/${year}`);

    // Check if report already exists
    const existingReport = await db
      .select()
      .from(linkReports)
      .where(
        and(
          eq(linkReports.link, link),
          eq(linkReports.type, type),
          eq(linkReports.fid, fid),
          eq(linkReports.month, month),
          eq(linkReports.year, year)
        )
      )
      .limit(1);

    if (existingReport.length > 0) {
      // Update existing report
      const newTotal = existingReport[0].totalOpen + 1;

      await db
        .update(linkReports)
        .set({
          totalOpen: newTotal,
        })
        .where(
          and(
            eq(linkReports.link, link),
            eq(linkReports.type, type),
            eq(linkReports.fid, fid),
            eq(linkReports.month, month),
            eq(linkReports.year, year)
          )
        );

      console.log(`Updated link report: ${link}/${type}/${fid}/${month}/${year} - Total: ${newTotal}`);
    } else {
      // Create new report
      await db.insert(linkReports).values({
        link,
        type,
        fid,
        month,
        year,
        totalOpen: 1,
      });

      console.log(`Created new link report: ${link}/${type}/${fid}/${month}/${year}`);
    }

    // Update link statistics
    await updateLinkStatistics(link, type, fid, month, year);

    console.log(`Link report job completed: ${link}/${type}/${fid}/${month}/${year}`);
  } catch (error) {
    console.error(`Link report job failed: ${link}/${type}/${fid}/${month}/${year}`, error);
    throw error;
  }
};

// Helper function to update link statistics
const updateLinkStatistics = async (
  link: string,
  type: number,
  fid: string,
  month: number,
  year: number
): Promise<void> => {
  try {
    const { cacheService } = await import('../../config/redis');
    
    // Create cache keys
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const linkHash = Buffer.from(link).toString('base64').substring(0, 16); // Shortened hash for cache key
    
    // Update daily statistics
    const dailyKey = `link:stats:daily:${linkHash}:${currentDate}`;
    await cacheService.increment(dailyKey, 86400); // 24 hours TTL

    // Update monthly statistics
    const monthKey = `link:stats:monthly:${linkHash}:${year}-${String(month).padStart(2, '0')}`;
    await cacheService.increment(monthKey, 86400 * 31); // 31 days TTL

    // Update link type statistics
    const typeKey = `link:stats:type:${type}:${currentDate}`;
    await cacheService.increment(typeKey, 86400);

    // Update FID statistics
    const fidKey = `link:stats:fid:${fid}:${currentDate}`;
    await cacheService.increment(fidKey, 86400);

    // Update popular links cache
    const popularKey = `link:stats:popular:${year}-${String(month).padStart(2, '0')}`;
    const popularData = await cacheService.getHash(popularKey, linkHash) || { count: 0, link, type, fid };
    popularData.count += 1;
    popularData.lastClick = new Date().toISOString();
    
    await cacheService.setHash(popularKey, linkHash, popularData, 86400 * 31);

    // Update real-time link tracking
    const realtimeKey = `link:stats:realtime`;
    const realtimeData = await cacheService.get(realtimeKey) || [];
    
    // Add new click to realtime data (keep last 100 clicks)
    realtimeData.unshift({
      link,
      type,
      fid,
      timestamp: new Date().toISOString(),
      date: currentDate,
    });
    
    // Keep only last 100 clicks
    if (realtimeData.length > 100) {
      realtimeData.splice(100);
    }
    
    await cacheService.set(realtimeKey, realtimeData, 3600); // 1 hour TTL

    console.log(`Updated link statistics: ${link}/${type}/${fid}/${month}/${year}`);
  } catch (error) {
    console.error(`Failed to update link statistics: ${link}/${type}/${fid}/${month}/${year}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Helper function to get link domain for analytics
const getLinkDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return 'unknown';
  }
};

// Job failure handler
export const handleLinkReportJobFailure = async (job: Job<LinkReportJobData>, error: Error): Promise<void> => {
  const { link, type, fid, month, year } = job.data;
  console.error(`Link report job failed permanently: ${link}/${type}/${fid}/${month}/${year}`, {
    error: error.message,
    stack: error.stack,
    attempts: job.attemptsMade,
    data: job.data,
  });

  // Could send notification or log to external service here
};

// Job completion handler
export const handleLinkReportJobCompletion = async (job: Job<LinkReportJobData>): Promise<void> => {
  const { link, type, fid, month, year } = job.data;
  console.log(`Link report job completed successfully: ${link}/${type}/${fid}/${month}/${year}`);
  
  // Could update metrics or send notifications here
};
