import { Job } from 'bullmq';
import { db } from '../../config/database';
import { gameReports } from '../../models/schemas';
import { eq, and } from 'drizzle-orm';

// Job data interface
export interface GameReportJobData {
  fid: string;
  code: string;
  uid?: string;
  month: number;
  year: number;
}

// Job options
export const gameReportJobOptions = {
  removeOnComplete: 10,
  removeOnFail: 5,
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
};

// Job processor
export const processGameReport = async (job: Job<GameReportJobData>): Promise<void> => {
  const { fid, code, uid, month, year } = job.data;

  try {
    // Log job start
    console.log(`Processing game report job: ${fid}/${code}/${month}/${year}`);

    // Check if report already exists
    const existingReport = await db
      .select()
      .from(gameReports)
      .where(
        and(
          eq(gameReports.code, code),
          eq(gameReports.fid, fid),
          eq(gameReports.month, month),
          eq(gameReports.year, year)
        )
      )
      .limit(1);

    if (existingReport.length > 0) {
      // Update existing report
      await db
        .update(gameReports)
        .set({
          totalOpen: existingReport[0].totalOpen + 1,
        })
        .where(
          and(
            eq(gameReports.code, code),
            eq(gameReports.fid, fid),
            eq(gameReports.month, month),
            eq(gameReports.year, year)
          )
        );

      console.log(`Updated game report: ${fid}/${code}/${month}/${year} - Total: ${existingReport[0].totalOpen + 1}`);
    } else {
      // Create new report
      await db.insert(gameReports).values({
        code,
        fid,
        month,
        year,
        totalOpen: 1,
      });

      console.log(`Created new game report: ${fid}/${code}/${month}/${year}`);
    }

    // Update user game report if uid is provided
    if (uid) {
      await updateUserGameReport(fid, uid, code);
    }

    console.log(`Game report job completed: ${fid}/${code}/${month}/${year}`);
  } catch (error) {
    console.error(`Game report job failed: ${fid}/${code}/${month}/${year}`, error);
    throw error;
  }
};

// Helper function to update user game report
const updateUserGameReport = async (fid: string, uid: string, code: string): Promise<void> => {
  try {
    const { userGameReports } = await import('../../models/schemas');
    
    // Check if user game report exists
    const existingUserReport = await db
      .select()
      .from(userGameReports)
      .where(
        and(
          eq(userGameReports.fid, fid),
          eq(userGameReports.uid, uid)
        )
      )
      .limit(1);

    if (existingUserReport.length > 0) {
      // Parse existing games data
      const gamesData = JSON.parse(existingUserReport[0].games);
      
      // Update or add game code
      if (gamesData[code]) {
        gamesData[code].count = (gamesData[code].count || 0) + 1;
        gamesData[code].lastPlayed = new Date().toISOString();
      } else {
        gamesData[code] = {
          count: 1,
          firstPlayed: new Date().toISOString(),
          lastPlayed: new Date().toISOString(),
        };
      }

      // Update user game report
      await db
        .update(userGameReports)
        .set({
          games: JSON.stringify(gamesData),
        })
        .where(
          and(
            eq(userGameReports.fid, fid),
            eq(userGameReports.uid, uid)
          )
        );
    } else {
      // Create new user game report
      const gamesData = {
        [code]: {
          count: 1,
          firstPlayed: new Date().toISOString(),
          lastPlayed: new Date().toISOString(),
        },
      };

      await db.insert(userGameReports).values({
        fid,
        uid,
        games: JSON.stringify(gamesData),
      });
    }

    console.log(`Updated user game report: ${fid}/${uid}/${code}`);
  } catch (error) {
    console.error(`Failed to update user game report: ${fid}/${uid}/${code}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Job failure handler
export const handleGameReportJobFailure = async (job: Job<GameReportJobData>, error: Error): Promise<void> => {
  const { fid, code, month, year } = job.data;
  console.error(`Game report job failed permanently: ${fid}/${code}/${month}/${year}`, {
    error: error.message,
    stack: error.stack,
    attempts: job.attemptsMade,
    data: job.data,
  });

  // Could send notification or log to external service here
};

// Job completion handler
export const handleGameReportJobCompletion = async (job: Job<GameReportJobData>): Promise<void> => {
  const { fid, code, month, year } = job.data;
  console.log(`Game report job completed successfully: ${fid}/${code}/${month}/${year}`);
  
  // Could update metrics or send notifications here
};
