import { BaseService } from '../base/BaseService';
import { BannerRepository } from '../../repositories/website/BannerRepository';
import { CacheService } from '../../config/redis';
import { QueueService } from '../../queues';
import { PaginatedResult, PaginationParams } from '../../types/database';
import { Banner, BannerV2 } from '../../models/schemas';
import { BadRequestError } from '../../utils/errors/badRequest.error';
import { NotFoundError } from '../../utils/errors/notFound.error';

export class BannerService extends BaseService {
  private bannerRepository: BannerRepository;

  constructor(
    bannerRepository: BannerRepository,
    cacheService: CacheService,
    queueService: typeof QueueService = QueueService
  ) {
    super(bannerRepository, cacheService, queueService);
    this.bannerRepository = bannerRepository;
  }

  // Get list of banners (v1)
  async getListBanners(query: any): Promise<Banner[]> {
    return this.measurePerformance('getListBanners', async () => {
      const cacheKey = this.generateCacheKey('banners:list', JSON.stringify(query));
      
      return this.getCachedList(cacheKey, async () => {
        const filters = this.buildBannerFilters(query);
        return await this.bannerRepository.findMany(filters, {
          limit: query.limit || 50,
          orderBy: this.bannerRepository.buildOrderBy('rank', 'asc'),
        });
      }, 1800); // 30 minutes cache
    });
  }

  // Get list of banners (v2)
  async getListBannersV2(query: any): Promise<PaginatedResult<BannerV2>> {
    return this.measurePerformance('getListBannersV2', async () => {
      const cacheKey = this.generateCacheKey('banners:v2:list', JSON.stringify(query));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        const filters = this.buildBannerV2Filters(query);
        const pagination = {
          page: query.page || 1,
          limit: query.limit || 10,
        };
        
        return await this.bannerRepository.findBannersV2WithPagination(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Get banner detail
  async getBannerDetail(id: number): Promise<Banner | null> {
    return this.measurePerformance('getBannerDetail', async () => {
      const cacheKey = this.generateCacheKey('banners:detail', id.toString());
      
      return this.getCachedDetail(cacheKey, async () => {
        return await this.bannerRepository.findById(id);
      }, 3600); // 1 hour cache
    });
  }

  // Create banner
  async createBanner(data: any): Promise<Banner> {
    return this.measurePerformance('createBanner', async () => {
      // Validate banner data
      await this.validateBannerData(data);

      const result = await this.createWithCacheInvalidation(data, [
        'banners:*',
        `banners:position:${data.positionId}:*`,
        `banners:fid:${data.fid}:*`,
      ]);

      return result;
    });
  }

  // Update banner
  async updateBanner(id: number, data: any): Promise<Banner | null> {
    return this.measurePerformance('updateBanner', async () => {
      const existing = await this.bannerRepository.findById(id);
      if (!existing) {
        return null;
      }

      const result = await this.updateWithCacheInvalidation(id, data, [
        'banners:*',
        `banners:detail:${id}`,
        `banners:position:${existing.positionId}:*`,
        `banners:fid:${existing.fid}:*`,
      ]);

      return result;
    });
  }

  // Delete banner
  async deleteBanner(id: number): Promise<boolean> {
    return this.measurePerformance('deleteBanner', async () => {
      const existing = await this.bannerRepository.findById(id);
      if (!existing) {
        return false;
      }

      const result = await this.deleteWithCacheInvalidation(id, [
        'banners:*',
        `banners:detail:${id}`,
        `banners:position:${existing.positionId}:*`,
        `banners:fid:${existing.fid}:*`,
      ]);

      return result;
    });
  }

  // Get banner analytics
  async getBannerAnalytics(filters: any, pagination: PaginationParams): Promise<any> {
    return this.measurePerformance('getBannerAnalytics', async () => {
      const cacheKey = this.generateCacheKey('banners:analytics', JSON.stringify(filters), JSON.stringify(pagination));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.bannerRepository.getBannerAnalytics(filters, pagination);
      }, 1800); // 30 minutes cache
    });
  }

  // Bulk banner operations
  async bulkBannerOperation(
    operation: string,
    bannerIds: number[],
    newPositionId?: number,
    reason?: string
  ): Promise<{
    successful: number[];
    failed: Array<{ item: number; error: string }>;
    total: number;
  }> {
    return this.measurePerformance('bulkBannerOperation', async () => {
      const successful: number[] = [];
      const failed: Array<{ item: number; error: string }> = [];

      for (const bannerId of bannerIds) {
        try {
          switch (operation) {
            case 'activate':
              await this.bannerRepository.updateStatus(bannerId, 1);
              break;
            case 'deactivate':
              await this.bannerRepository.updateStatus(bannerId, 0);
              break;
            case 'delete':
              await this.bannerRepository.delete(bannerId);
              break;
            case 'change_position':
              if (!newPositionId) {
                throw new Error('New position ID is required for change_position operation');
              }
              await this.bannerRepository.updatePosition(bannerId, newPositionId);
              break;
            default:
              throw new Error(`Unknown operation: ${operation}`);
          }
          successful.push(bannerId);
        } catch (error) {
          failed.push({
            item: bannerId,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Invalidate caches if any operations were successful
      if (successful.length > 0) {
        await this.invalidateCache('banners:*');
      }

      return {
        successful,
        failed,
        total: bannerIds.length,
      };
    });
  }

  // Search banners
  async searchBanners(
    query: string,
    filters: any,
    pagination: PaginationParams,
    sort: { field: string; direction: 'asc' | 'desc' }
  ): Promise<PaginatedResult<Banner>> {
    return this.measurePerformance('searchBanners', async () => {
      const cacheKey = this.generateCacheKey('banners:search', query, JSON.stringify(filters), JSON.stringify(pagination), JSON.stringify(sort));
      
      return this.getCachedPaginatedList(cacheKey, async () => {
        return await this.bannerRepository.searchBanners(query, filters, pagination, sort);
      }, 900); // 15 minutes cache
    });
  }

  // Get banner positions
  async getBannerPositions(filters: any): Promise<any[]> {
    return this.measurePerformance('getBannerPositions', async () => {
      const cacheKey = this.generateCacheKey('banners:positions', JSON.stringify(filters));
      
      return this.getCachedList(cacheKey, async () => {
        return await this.bannerRepository.getBannerPositions(filters);
      }, 7200); // 2 hours cache
    });
  }

  // Get banner statistics
  async getBannerStats(filters: any): Promise<any> {
    return this.measurePerformance('getBannerStats', async () => {
      const cacheKey = this.generateCacheKey('banners:stats', JSON.stringify(filters));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.bannerRepository.getBannerStats(filters);
      }, 1800); // 30 minutes cache
    });
  }

  // Get trending banners
  async getTrendingBanners(limit: number, period: string): Promise<Banner[]> {
    return this.measurePerformance('getTrendingBanners', async () => {
      const cacheKey = this.generateCacheKey('banners:trending', limit.toString(), period);
      
      return this.getCachedList(cacheKey, async () => {
        return await this.bannerRepository.getTrendingBanners(limit, period);
      }, 3600); // 1 hour cache
    });
  }

  // Schedule banner
  async scheduleBanner(bannerId: number, schedule: any): Promise<{ success: boolean; message: string }> {
    return this.measurePerformance('scheduleBanner', async () => {
      const banner = await this.bannerRepository.findById(bannerId);
      if (!banner) {
        throw new NotFoundError('Banner not found');
      }

      // Create banner time slots
      await this.bannerRepository.createTimeSlots(bannerId, schedule.timeSlots);

      // Invalidate related caches
      await this.invalidateCache(`banners:detail:${bannerId}`);
      await this.invalidateCache('banners:*');

      return {
        success: true,
        message: 'Banner scheduled successfully',
      };
    });
  }

  // Get banner performance
  async getBannerPerformance(id: number, period: string, metrics: string[]): Promise<any> {
    return this.measurePerformance('getBannerPerformance', async () => {
      const cacheKey = this.generateCacheKey('banners:performance', id.toString(), period, metrics.join(','));
      
      return this.getCachedOrFetch(cacheKey, async () => {
        return await this.bannerRepository.getBannerPerformance(id, period, metrics);
      }, 1800); // 30 minutes cache
    });
  }

  // Helper methods
  private buildBannerFilters(query: any): Partial<Banner> {
    const filters: Partial<Banner> = {};

    if (query.fid) {
      filters.fid = query.fid;
    }

    if (query.positionId) {
      filters.positionId = parseInt(query.positionId);
    }

    if (query.status !== undefined) {
      filters.status = parseInt(query.status);
    }

    return filters;
  }

  private buildBannerV2Filters(query: any): any {
    const filters: any = {};

    if (query.region) {
      filters.region = query.region;
    }

    if (query.city) {
      filters.city = query.city;
    }

    if (query.fid) {
      filters.fid = query.fid;
    }

    if (query.positionId) {
      filters.positionId = parseInt(query.positionId);
    }

    if (query.clientId) {
      filters.clientId = parseInt(query.clientId);
    }

    if (query.status !== undefined) {
      filters.status = parseInt(query.status);
    }

    return filters;
  }

  private async validateBannerData(data: any): Promise<void> {
    // Validate position exists
    const positions = await this.getBannerPositions({ status: 1 });
    const positionExists = positions.some(p => p.id === data.positionId);
    
    if (!positionExists) {
      throw new BadRequestError('Invalid banner position');
    }

    // Additional validation logic can be added here
  }
}
