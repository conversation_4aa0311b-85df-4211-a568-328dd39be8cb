import { APIError } from './base.error';

export class UnauthorizedError extends APIError {
  public readonly statusCode = 401;
  public readonly isOperational = true;

  constructor(message: string = 'Unauthorized') {
    super(message);
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, UnauthorizedError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
    };
  }
}
