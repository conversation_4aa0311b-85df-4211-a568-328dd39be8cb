import { z } from 'zod';

// Common validation schemas
export const idSchema = z.number().int().positive('ID must be a positive integer');

export const fidSchema = z.string().min(1, 'FID is required').max(255, 'FID is too long');

export const uidSchema = z.string().min(1, 'UID is required').max(255, 'UID is too long');

export const statusSchema = z.number().int().min(0).max(1, 'Status must be 0 or 1');

export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1, 'Page must be at least 1').default(1),
  limit: z.coerce.number().int().min(1).max(100, 'Limit must be between 1 and 100').default(10),
  offset: z.coerce.number().int().min(0).optional(),
});

export const timestampSchema = z.coerce.number().int().positive('Timestamp must be a positive integer');

export const signatureSchema = z.string().min(1, 'Signature is required');

// Common request schemas
export const paramsWithIdSchema = z.object({
  id: idSchema,
});

export const paramsWithFidSchema = z.object({
  fid: fidSchema,
});

export const paramsWithCodeSchema = z.object({
  code: z.string().min(1, 'Code is required').max(255, 'Code is too long'),
});

// Query schemas
export const baseQuerySchema = z.object({
  search: z.string().optional(),
  status: statusSchema.optional(),
  ...paginationSchema.shape,
});

// Headers schemas
export const signatureHeadersSchema = z.object({
  'x-timestamp': z.string().min(1, 'Timestamp header is required'),
  'x-signature': z.string().min(1, 'Signature header is required'),
});

export const versionHeadersSchema = z.object({
  'x-client-version': z.string().optional(),
});

// File upload schemas
export const fileSchema = z.object({
  fieldname: z.string(),
  originalname: z.string(),
  encoding: z.string(),
  mimetype: z.string(),
  size: z.number().positive(),
  buffer: z.instanceof(Buffer),
});

export const imageFileSchema = fileSchema.extend({
  mimetype: z.string().regex(/^image\/(jpeg|jpg|png|gif|webp)$/, 'Invalid image format'),
  size: z.number().max(5 * 1024 * 1024, 'Image size must be less than 5MB'),
});

// Date schemas
export const dateStringSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format');

export const dateTimeStringSchema = z.string().regex(
  /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
  'DateTime must be in YYYY-MM-DD HH:MM:SS format'
);

// URL schemas
export const urlSchema = z.string().url('Invalid URL format');

export const optionalUrlSchema = z.string().url('Invalid URL format').optional().or(z.literal(''));

// Email schema
export const emailSchema = z.string().email('Invalid email format');

// Phone schema
export const phoneSchema = z.string().regex(/^[+]?[\d\s\-\(\)]{10,20}$/, 'Invalid phone number format');

// Password schema
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(255, 'Password is too long')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase, one uppercase, and one number');

// JSON schema
export const jsonStringSchema = z.string().refine((val) => {
  try {
    JSON.parse(val);
    return true;
  } catch {
    return false;
  }
}, 'Invalid JSON format');

// Color schema
export const colorSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format (must be hex)');

// Slug schema
export const slugSchema = z.string().regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens');

// Sort order schema
export const sortOrderSchema = z.enum(['asc', 'desc']);

// Order by schema
export const orderBySchema = z.object({
  field: z.string().min(1, 'Field is required'),
  direction: sortOrderSchema,
});

// IP address schema
export const ipAddressSchema = z.string().ip('Invalid IP address');

// User agent schema
export const userAgentSchema = z.string().max(500, 'User agent is too long');

// Coordinate schemas
export const latitudeSchema = z.number().min(-90).max(90, 'Latitude must be between -90 and 90');

export const longitudeSchema = z.number().min(-180).max(180, 'Longitude must be between -180 and 180');

// Text content schemas
export const shortTextSchema = z.string().max(255, 'Text is too long');

export const mediumTextSchema = z.string().max(1000, 'Text is too long');

export const longTextSchema = z.string().max(5000, 'Text is too long');

// HTML content schema
export const htmlContentSchema = z.string().max(10000, 'HTML content is too long');

// Base entity schemas
export const baseEntitySchema = z.object({
  id: idSchema,
  createdAt: z.date(),
  updatedAt: z.date().optional(),
  createdBy: idSchema.optional(),
  updatedBy: idSchema.optional(),
});

export const softDeleteEntitySchema = baseEntitySchema.extend({
  deletedAt: z.date().optional(),
  deletedBy: idSchema.optional(),
});

// Utility functions
export const createArraySchema = <T extends z.ZodTypeAny>(itemSchema: T, minItems = 1, maxItems = 100) =>
  z.array(itemSchema).min(minItems, `At least ${minItems} item(s) required`).max(maxItems, `Maximum ${maxItems} items allowed`);

export const createOptionalArraySchema = <T extends z.ZodTypeAny>(itemSchema: T, maxItems = 100) =>
  z.array(itemSchema).max(maxItems, `Maximum ${maxItems} items allowed`).optional();

export const createEnumSchema = <T extends readonly [string, ...string[]]>(values: T, message?: string) =>
  z.enum(values, { errorMap: () => ({ message: message || `Value must be one of: ${values.join(', ')}` }) });

// Transform schemas
export const stringToNumberSchema = z.string().transform((val, ctx) => {
  const parsed = parseInt(val);
  if (isNaN(parsed)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Not a number',
    });
    return z.NEVER;
  }
  return parsed;
});

export const stringToBooleanSchema = z.string().transform((val) => {
  return val === 'true' || val === '1';
});

export const stringToDateSchema = z.string().transform((val, ctx) => {
  const parsed = new Date(val);
  if (isNaN(parsed.getTime())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Invalid date',
    });
    return z.NEVER;
  }
  return parsed;
});
