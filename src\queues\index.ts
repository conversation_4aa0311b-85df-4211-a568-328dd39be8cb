import { Queue, QueueOptions } from 'bullmq';
import { queueRedisClient } from '../config/redis';

// Import job data types
import { GameReportJobData, gameReportJobOptions } from './jobs/gameReport.job';
import { BannerReportJobData, bannerReportJobOptions } from './jobs/bannerReport.job';
import { LinkReportJobData, linkReportJobOptions } from './jobs/linkReport.job';
import { LogEventJobData, logEventJobOptions } from './jobs/logEvent.job';
import { UpdateLocationJobData, updateLocationJobOptions } from './jobs/updateLocation.job';

// Queue configuration
const defaultQueueOptions: QueueOptions = {
  connection: queueRedisClient,
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Create queues
export const gameReportQueue = new Queue<GameReportJobData>('gameReport', defaultQueueOptions);
export const bannerReportQueue = new Queue<BannerReportJobData>('bannerReport', defaultQueueOptions);
export const linkReportQueue = new Queue<LinkReportJobData>('linkReport', defaultQueueOptions);
export const logEventQueue = new Queue<LogEventJobData>('logEvent', {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    removeOnComplete: 50,
    attempts: 2,
  },
});
export const updateLocationQueue = new Queue<UpdateLocationJobData>('updateLocation', defaultQueueOptions);

// Array of all queues for management
export const queues = [
  gameReportQueue,
  bannerReportQueue,
  linkReportQueue,
  logEventQueue,
  updateLocationQueue,
];

// Queue service class
export class QueueService {
  // Game Report Queue Methods
  static async addGameReportJob(data: GameReportJobData, options = gameReportJobOptions) {
    return await gameReportQueue.add('updateGameReport', data, options);
  }

  // Banner Report Queue Methods
  static async addBannerReportJob(data: BannerReportJobData, options = bannerReportJobOptions) {
    return await bannerReportQueue.add('updateBannerReport', data, options);
  }

  // Link Report Queue Methods
  static async addLinkReportJob(data: LinkReportJobData, options = linkReportJobOptions) {
    return await linkReportQueue.add('updateLinkReport', data, options);
  }

  // Log Event Queue Methods
  static async addLogEventJob(data: LogEventJobData, options = logEventJobOptions) {
    return await logEventQueue.add('logEvent', data, options);
  }

  // Update Location Queue Methods
  static async addUpdateLocationJob(data: UpdateLocationJobData, options = updateLocationJobOptions) {
    return await updateLocationQueue.add('updateLocation', data, options);
  }

  // Bulk job methods
  static async addBulkGameReportJobs(jobs: Array<{ data: GameReportJobData; options?: any }>) {
    const bulkJobs = jobs.map((job, index) => ({
      name: `updateGameReport-${index}`,
      data: job.data,
      opts: { ...gameReportJobOptions, ...job.options },
    }));
    
    return await gameReportQueue.addBulk(bulkJobs);
  }

  static async addBulkLogEventJobs(jobs: Array<{ data: LogEventJobData; options?: any }>) {
    const bulkJobs = jobs.map((job, index) => ({
      name: `logEvent-${index}`,
      data: job.data,
      opts: { ...logEventJobOptions, ...job.options },
    }));
    
    return await logEventQueue.addBulk(bulkJobs);
  }

  // Queue management methods
  static async getQueueStats(queueName: string) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      name: queue.name,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      total: waiting.length + active.length + completed.length + failed.length + delayed.length,
    };
  }

  static async getAllQueueStats() {
    const stats = await Promise.all(
      queues.map(queue => this.getQueueStats(queue.name))
    );
    
    return stats.reduce((acc, stat) => {
      acc[stat.name] = stat;
      return acc;
    }, {} as Record<string, any>);
  }

  static async pauseQueue(queueName: string) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }
    
    await queue.pause();
    console.log(`Queue ${queueName} paused`);
  }

  static async resumeQueue(queueName: string) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }
    
    await queue.resume();
    console.log(`Queue ${queueName} resumed`);
  }

  static async pauseAllQueues() {
    await Promise.all(queues.map(queue => queue.pause()));
    console.log('All queues paused');
  }

  static async resumeAllQueues() {
    await Promise.all(queues.map(queue => queue.resume()));
    console.log('All queues resumed');
  }

  static async cleanQueue(queueName: string, grace: number = 0, limit: number = 100) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const results = await Promise.all([
      queue.clean(grace, limit, 'completed'),
      queue.clean(grace, limit, 'failed'),
    ]);

    console.log(`Cleaned queue ${queueName}: ${results[0]} completed, ${results[1]} failed jobs removed`);
    return results;
  }

  static async cleanAllQueues(grace: number = 0, limit: number = 100) {
    const results = await Promise.all(
      queues.map(queue => this.cleanQueue(queue.name, grace, limit))
    );
    
    return results;
  }

  static async drainQueue(queueName: string) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.drain();
    console.log(`Queue ${queueName} drained`);
  }

  static async drainAllQueues() {
    await Promise.all(queues.map(queue => queue.drain()));
    console.log('All queues drained');
  }

  // Job retry methods
  static async retryFailedJobs(queueName: string, limit: number = 100) {
    const queue = queues.find(q => q.name === queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const failedJobs = await queue.getFailed(0, limit - 1);
    const retryPromises = failedJobs.map(job => job.retry());
    
    await Promise.all(retryPromises);
    console.log(`Retried ${failedJobs.length} failed jobs in queue ${queueName}`);
    
    return failedJobs.length;
  }

  static async retryAllFailedJobs(limit: number = 100) {
    const results = await Promise.all(
      queues.map(queue => this.retryFailedJobs(queue.name, limit))
    );
    
    const totalRetried = results.reduce((sum, count) => sum + count, 0);
    console.log(`Retried ${totalRetried} failed jobs across all queues`);
    
    return totalRetried;
  }

  // Health check
  static async getHealthStatus() {
    try {
      const stats = await this.getAllQueueStats();
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        queues: stats,
        redis: {
          status: queueRedisClient.status,
        },
      };

      return health;
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Graceful shutdown
export const shutdownQueues = async (): Promise<void> => {
  console.log('🔄 Shutting down queues...');
  
  const shutdownPromises = queues.map(async (queue) => {
    try {
      await queue.close();
      console.log(`✅ Queue ${queue.name} shut down successfully`);
    } catch (error) {
      console.error(`❌ Error shutting down queue ${queue.name}:`, error);
    }
  });

  await Promise.all(shutdownPromises);
  console.log('✅ All queues shut down');
};

// Export individual queues and service
export {
  GameReportJobData,
  BannerReportJobData,
  LinkReportJobData,
  LogEventJobData,
  UpdateLocationJobData,
};
