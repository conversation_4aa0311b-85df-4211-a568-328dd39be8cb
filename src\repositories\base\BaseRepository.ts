import { Dr<PERSON>zleD<PERSON>, PaginatedResult, PaginationParams } from '../../types/database';
import { SQL, eq, and, or, desc, asc, count } from 'drizzle-orm';
import { MySqlTable } from 'drizzle-orm/mysql-core';

export abstract class BaseRepository<T> {
  protected readonly db: DrizzleDB;
  protected readonly table: MySqlTable;

  constructor(db: DrizzleDB, table: MySqlTable) {
    this.db = db;
    this.table = table;
  }

  // Basic CRUD operations
  async findById(id: number): Promise<T | null> {
    try {
      const result = await this.db
        .select()
        .from(this.table)
        .where(eq(this.table.id, id))
        .limit(1);

      return result[0] as T || null;
    } catch (error) {
      console.error(`Error finding record by ID ${id}:`, error);
      throw error;
    }
  }

  async findMany(
    filter: Partial<T> = {},
    options: {
      limit?: number;
      offset?: number;
      orderBy?: SQL;
      select?: any;
    } = {}
  ): Promise<T[]> {
    try {
      let query = this.db.select(options.select).from(this.table);

      // Apply filters
      const whereClause = this.buildWhereClause(filter);
      if (whereClause) {
        query = query.where(whereClause);
      }

      // Apply ordering
      if (options.orderBy) {
        query = query.orderBy(options.orderBy);
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.offset(options.offset);
      }

      return await query as T[];
    } catch (error) {
      console.error('Error finding records:', error);
      throw error;
    }
  }

  async findWithPagination(
    filter: Partial<T> = {},
    pagination: PaginationParams = {},
    options: {
      orderBy?: SQL;
      select?: any;
    } = {}
  ): Promise<PaginatedResult<T>> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      const whereClause = this.buildWhereClause(filter);

      // Get total count
      let countQuery = this.db.select({ count: count() }).from(this.table);
      if (whereClause) {
        countQuery = countQuery.where(whereClause);
      }
      const totalResult = await countQuery;
      const totalItems = totalResult[0]?.count || 0;

      // Get items
      let itemsQuery = this.db.select(options.select).from(this.table);
      if (whereClause) {
        itemsQuery = itemsQuery.where(whereClause);
      }
      if (options.orderBy) {
        itemsQuery = itemsQuery.orderBy(options.orderBy);
      }
      itemsQuery = itemsQuery.limit(limit).offset(offset);

      const items = await itemsQuery as T[];

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
      };
    } catch (error) {
      console.error('Error finding records with pagination:', error);
      throw error;
    }
  }

  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    try {
      const insertData = {
        ...data,
        createdAt: new Date(),
      };

      const result = await this.db.insert(this.table).values(insertData);
      const insertId = result[0].insertId;

      // Return the created record
      return await this.findById(insertId) as T;
    } catch (error) {
      console.error('Error creating record:', error);
      throw error;
    }
  }

  async update(id: number, data: Partial<T>): Promise<T | null> {
    try {
      const updateData = {
        ...data,
        updatedAt: new Date(),
      };

      await this.db
        .update(this.table)
        .set(updateData)
        .where(eq(this.table.id, id));

      // Return the updated record
      return await this.findById(id);
    } catch (error) {
      console.error(`Error updating record ${id}:`, error);
      throw error;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const result = await this.db
        .delete(this.table)
        .where(eq(this.table.id, id));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error deleting record ${id}:`, error);
      throw error;
    }
  }

  // Soft delete operations (for tables that support it)
  async softDelete(id: number, deletedBy?: number): Promise<boolean> {
    try {
      const updateData: any = {
        deletedAt: new Date(),
        updatedAt: new Date(),
      };

      if (deletedBy) {
        updateData.deletedBy = deletedBy;
      }

      const result = await this.db
        .update(this.table)
        .set(updateData)
        .where(eq(this.table.id, id));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error soft deleting record ${id}:`, error);
      throw error;
    }
  }

  async restore(id: number): Promise<boolean> {
    try {
      const result = await this.db
        .update(this.table)
        .set({
          deletedAt: null,
          deletedBy: null,
          updatedAt: new Date(),
        })
        .where(eq(this.table.id, id));

      return result[0].affectedRows > 0;
    } catch (error) {
      console.error(`Error restoring record ${id}:`, error);
      throw error;
    }
  }

  // Bulk operations
  async bulkCreate(data: Array<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>): Promise<T[]> {
    try {
      const insertData = data.map(item => ({
        ...item,
        createdAt: new Date(),
      }));

      const result = await this.db.insert(this.table).values(insertData);
      const firstInsertId = result[0].insertId;

      // Return the created records
      const ids = Array.from({ length: data.length }, (_, i) => firstInsertId + i);
      return await this.findByIds(ids);
    } catch (error) {
      console.error('Error bulk creating records:', error);
      throw error;
    }
  }

  async bulkUpdate(updates: Array<{ id: number; data: Partial<T> }>): Promise<T[]> {
    try {
      const updatePromises = updates.map(({ id, data }) => this.update(id, data));
      const results = await Promise.all(updatePromises);
      return results.filter(result => result !== null) as T[];
    } catch (error) {
      console.error('Error bulk updating records:', error);
      throw error;
    }
  }

  async bulkDelete(ids: number[]): Promise<number> {
    try {
      const result = await this.db
        .delete(this.table)
        .where(this.buildInClause(this.table.id, ids));

      return result[0].affectedRows;
    } catch (error) {
      console.error('Error bulk deleting records:', error);
      throw error;
    }
  }

  // Helper methods
  async findByIds(ids: number[]): Promise<T[]> {
    try {
      return await this.db
        .select()
        .from(this.table)
        .where(this.buildInClause(this.table.id, ids)) as T[];
    } catch (error) {
      console.error('Error finding records by IDs:', error);
      throw error;
    }
  }

  async exists(id: number): Promise<boolean> {
    try {
      const result = await this.db
        .select({ id: this.table.id })
        .from(this.table)
        .where(eq(this.table.id, id))
        .limit(1);

      return result.length > 0;
    } catch (error) {
      console.error(`Error checking if record ${id} exists:`, error);
      throw error;
    }
  }

  async count(filter: Partial<T> = {}): Promise<number> {
    try {
      let query = this.db.select({ count: count() }).from(this.table);

      const whereClause = this.buildWhereClause(filter);
      if (whereClause) {
        query = query.where(whereClause);
      }

      const result = await query;
      return result[0]?.count || 0;
    } catch (error) {
      console.error('Error counting records:', error);
      throw error;
    }
  }

  // Query building helpers
  protected buildWhereClause(filter: Partial<T>): SQL | undefined {
    const conditions: SQL[] = [];

    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (this.table[key]) {
          conditions.push(eq(this.table[key], value));
        }
      }
    });

    if (conditions.length === 0) {
      return undefined;
    }

    return conditions.length === 1 ? conditions[0] : and(...conditions);
  }

  protected buildInClause(column: any, values: any[]): SQL {
    if (values.length === 0) {
      throw new Error('Cannot build IN clause with empty values array');
    }

    const conditions = values.map(value => eq(column, value));
    return or(...conditions)!;
  }

  protected buildOrderBy(field: string, direction: 'asc' | 'desc' = 'asc'): SQL {
    const column = this.table[field];
    if (!column) {
      throw new Error(`Invalid sort field: ${field}`);
    }

    return direction === 'desc' ? desc(column) : asc(column);
  }

  // Transaction support
  async transaction<R>(
    callback: (tx: DrizzleDB) => Promise<R>
  ): Promise<R> {
    return await this.db.transaction(callback);
  }
}
