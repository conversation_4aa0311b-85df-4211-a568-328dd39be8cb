import { Job } from 'bullmq';
import { logDB } from '../../config/database';
import { events } from '../../models/schemas';

// Job data interface
export interface LogEventJobData {
  fid: string;
  uid?: string;
  pcn?: string;
  version?: string;
  eventType: string;
  eventData?: any;
  ipAddress?: string;
  userAgent?: string;
}

// Job options
export const logEventJobOptions = {
  removeOnComplete: 50,
  removeOnFail: 10,
  attempts: 2,
  backoff: {
    type: 'fixed' as const,
    delay: 1000,
  },
};

// Job processor
export const processLogEvent = async (job: Job<LogEventJobData>): Promise<void> => {
  const { fid, uid, pcn, version, eventType, eventData, ipAddress, userAgent } = job.data;

  try {
    console.log(`Processing log event job: ${fid}/${uid}/${eventType}`);

    // Insert event into log database
    await logDB.insert(events).values({
      fid,
      uid: uid || null,
      pcn: pcn || null,
      version: version || null,
      eventType,
      eventData: eventData ? JSON.stringify(eventData) : null,
      ipAddress: ipAddress || null,
      userAgent: userAgent || null,
      createdAt: new Date(),
    });

    // Update event statistics in cache
    await updateEventStatistics(fid, uid, eventType, eventData);

    console.log(`Log event job completed: ${fid}/${uid}/${eventType}`);
  } catch (error) {
    console.error(`Log event job failed: ${fid}/${uid}/${eventType}`, error);
    throw error;
  }
};

// Helper function to update event statistics
const updateEventStatistics = async (
  fid: string,
  uid: string | undefined,
  eventType: string,
  eventData: any
): Promise<void> => {
  try {
    const { cacheService } = await import('../../config/redis');
    
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const currentHour = new Date().getHours();

    // Update daily event counts
    const dailyKey = `events:daily:${fid}:${currentDate}`;
    await cacheService.increment(dailyKey, 86400); // 24 hours TTL

    // Update hourly event counts
    const hourlyKey = `events:hourly:${fid}:${currentDate}:${currentHour}`;
    await cacheService.increment(hourlyKey, 3600); // 1 hour TTL

    // Update event type statistics
    const eventTypeKey = `events:type:${eventType}:${currentDate}`;
    await cacheService.increment(eventTypeKey, 86400);

    // Update user activity if uid is provided
    if (uid) {
      const userActivityKey = `events:user:${fid}:${uid}:${currentDate}`;
      await cacheService.increment(userActivityKey, 86400);

      // Update user session data
      const sessionKey = `events:session:${fid}:${uid}`;
      const sessionData = await cacheService.get(sessionKey) || {
        firstEvent: new Date().toISOString(),
        eventCount: 0,
        eventTypes: [],
      };

      sessionData.lastEvent = new Date().toISOString();
      sessionData.eventCount += 1;
      
      if (!sessionData.eventTypes.includes(eventType)) {
        sessionData.eventTypes.push(eventType);
      }

      await cacheService.set(sessionKey, sessionData, 1800); // 30 minutes TTL
    }

    // Update real-time event stream
    const realtimeKey = `events:realtime:${fid}`;
    const realtimeData = await cacheService.get(realtimeKey) || [];
    
    realtimeData.unshift({
      eventType,
      uid,
      timestamp: new Date().toISOString(),
      data: eventData,
    });
    
    // Keep only last 50 events
    if (realtimeData.length > 50) {
      realtimeData.splice(50);
    }
    
    await cacheService.set(realtimeKey, realtimeData, 600); // 10 minutes TTL

    // Update event type popularity
    const popularEventTypesKey = `events:popular:types:${currentDate}`;
    const popularData = await cacheService.getHash(popularEventTypesKey, eventType) || { count: 0 };
    popularData.count += 1;
    popularData.lastSeen = new Date().toISOString();
    
    await cacheService.setHash(popularEventTypesKey, eventType, popularData, 86400);

    // Update FID activity summary
    const fidSummaryKey = `events:summary:${fid}:${currentDate}`;
    const summaryData = await cacheService.get(fidSummaryKey) || {
      totalEvents: 0,
      uniqueUsers: new Set(),
      eventTypes: {},
      firstEvent: new Date().toISOString(),
    };

    summaryData.totalEvents += 1;
    summaryData.lastEvent = new Date().toISOString();
    
    if (uid) {
      summaryData.uniqueUsers.add(uid);
    }
    
    if (!summaryData.eventTypes[eventType]) {
      summaryData.eventTypes[eventType] = 0;
    }
    summaryData.eventTypes[eventType] += 1;

    // Convert Set to Array for JSON serialization
    const summaryToStore = {
      ...summaryData,
      uniqueUsers: Array.from(summaryData.uniqueUsers),
      uniqueUserCount: summaryData.uniqueUsers.size,
    };
    
    await cacheService.set(fidSummaryKey, summaryToStore, 86400);

    console.log(`Updated event statistics: ${fid}/${uid}/${eventType}`);
  } catch (error) {
    console.error(`Failed to update event statistics: ${fid}/${uid}/${eventType}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Job failure handler
export const handleLogEventJobFailure = async (job: Job<LogEventJobData>, error: Error): Promise<void> => {
  const { fid, uid, eventType } = job.data;
  console.error(`Log event job failed permanently: ${fid}/${uid}/${eventType}`, {
    error: error.message,
    stack: error.stack,
    attempts: job.attemptsMade,
    data: job.data,
  });

  // Could send notification or log to external service here
};

// Job completion handler
export const handleLogEventJobCompletion = async (job: Job<LogEventJobData>): Promise<void> => {
  const { fid, uid, eventType } = job.data;
  console.log(`Log event job completed successfully: ${fid}/${uid}/${eventType}`);
  
  // Could update metrics or send notifications here
};
