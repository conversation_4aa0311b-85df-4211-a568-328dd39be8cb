// Export all website validators
export * from './game.validator';
export * from './banner.validator';
export * from './video.validator';
export * from './tracking.validator';

// Additional validators for other endpoints
import { z } from 'zod';
import {
  fidSchema,
  uidSchema,
  paginationSchema,
  baseQuerySchema,
  shortTextSchema,
  mediumTextSchema,
  longTextSchema,
  emailSchema,
  phoneSchema,
  urlSchema,
  statusSchema,
} from '../common/base.validator';

// News validators
export const getNewsListSchema = z.object({
  query: baseQuerySchema.extend({
    category: z.string().max(100).optional(),
    author: z.string().max(255).optional(),
    featured: z.enum(['0', '1']).optional(),
    sortBy: z.enum(['title', 'created_at', 'publish_date', 'views']).default('publish_date'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),
});

export const getFlashNewsListSchema = z.object({
  query: z.object({
    limit: z.coerce.number().int().min(1).max(50).default(10),
    status: statusSchema.optional(),
  }),
});

// Settings validators
export const getSettingsSchema = z.object({
  params: z.object({
    fid: fidSchema,
  }),
});

export const updateSettingsSchema = z.object({
  params: z.object({
    fid: fidSchema,
  }),
  body: z.object({
    settings: z.array(z.object({
      key: z.string().min(1).max(255),
      value: z.any(),
      type: z.enum(['string', 'number', 'boolean', 'json', 'url', 'email']).default('string'),
    })).min(1, 'At least one setting is required'),
  }),
});

// Location validators
export const getLocationDetailSchema = z.object({
  query: z.object({
    fid: fidSchema.optional(),
    regionId: z.coerce.number().int().positive().optional(),
    cityId: z.coerce.number().int().positive().optional(),
  }),
});

export const updateLocationSchema = z.object({
  body: z.object({
    fid: fidSchema,
    fuid: z.string().max(255).optional(),
    locationData: z.object({
      name: shortTextSchema.optional(),
      address: mediumTextSchema.optional(),
      latitude: z.number().min(-90).max(90).optional(),
      longitude: z.number().min(-180).max(180).optional(),
      phone: phoneSchema.optional(),
      email: emailSchema.optional(),
      website: urlSchema.optional(),
      description: longTextSchema.optional(),
    }),
  }),
});

// Feedback validators
export const sendFeedbackSchema = z.object({
  body: z.object({
    fid: fidSchema,
    uid: uidSchema,
    pcn: z.string().min(1, 'PCN is required').max(255),
    feedbackId: z.string().min(1, 'Feedback ID is required').max(255),
    feedbackTitle: z.string().min(1, 'Feedback title is required').max(500),
    content: longTextSchema.optional(),
    category: z.enum(['bug', 'feature', 'improvement', 'complaint', 'other']).optional(),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
    attachments: z.array(z.object({
      filename: z.string().max(255),
      url: urlSchema,
      size: z.number().int().positive(),
      mimeType: z.string().max(100),
    })).max(5).optional(),
  }),
});

// H5 Game validators
export const getH5GamesListSchema = z.object({
  query: baseQuerySchema.extend({
    category: z.string().max(100).optional(),
    trending: z.enum(['0', '1']).optional(),
    featured: z.enum(['0', '1']).optional(),
    sortBy: z.enum(['name', 'created_at', 'popularity', 'rating']).default('created_at'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),
});

export const getH5GameCategoriesSchema = z.object({
  query: z.object({
    status: statusSchema.optional(),
    sortBy: z.enum(['name', 'sort', 'created_at']).default('sort'),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
  }),
});

export const getTrendingH5GamesSchema = z.object({
  query: z.object({
    limit: z.coerce.number().int().min(1).max(50).default(10),
    period: z.enum(['day', 'week', 'month']).default('week'),
  }),
});

export const getUserH5GamesSchema = z.object({
  params: z.object({
    fid: fidSchema,
    uid: uidSchema,
  }),
  query: paginationSchema.extend({
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
    sortBy: z.enum(['play_count', 'last_played', 'score']).default('last_played'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),
});

// Health check validator
export const healthCheckSchema = z.object({
  query: z.object({
    detailed: z.enum(['0', '1']).default('0'),
  }),
});

// Common response schemas for documentation
export const successResponseSchema = z.object({
  status: z.literal('success'),
  code: z.number().int().min(200).max(299),
  message: z.string(),
  metadata: z.any().optional(),
});

export const errorResponseSchema = z.object({
  status: z.literal('error'),
  code: z.number().int().min(400).max(599),
  message: z.string(),
  errors: z.record(z.string()).optional(),
});

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  successResponseSchema.extend({
    metadata: z.object({
      items: z.array(itemSchema),
      totalItems: z.number().int().min(0),
      itemsPerPage: z.number().int().min(1),
      currentPage: z.number().int().min(1),
      totalPages: z.number().int().min(0),
    }),
  });

// Export types
export type GetNewsListRequest = z.infer<typeof getNewsListSchema>;
export type GetFlashNewsListRequest = z.infer<typeof getFlashNewsListSchema>;
export type GetSettingsRequest = z.infer<typeof getSettingsSchema>;
export type UpdateSettingsRequest = z.infer<typeof updateSettingsSchema>;
export type GetLocationDetailRequest = z.infer<typeof getLocationDetailSchema>;
export type UpdateLocationRequest = z.infer<typeof updateLocationSchema>;
export type SendFeedbackRequest = z.infer<typeof sendFeedbackSchema>;
export type GetH5GamesListRequest = z.infer<typeof getH5GamesListSchema>;
export type GetH5GameCategoriesRequest = z.infer<typeof getH5GameCategoriesSchema>;
export type GetTrendingH5GamesRequest = z.infer<typeof getTrendingH5GamesSchema>;
export type GetUserH5GamesRequest = z.infer<typeof getUserH5GamesSchema>;
export type HealthCheckRequest = z.infer<typeof healthCheckSchema>;
export type SuccessResponse = z.infer<typeof successResponseSchema>;
export type ErrorResponse = z.infer<typeof errorResponseSchema>;
