import { Request, Response, NextFunction } from 'express';
import { z, ZodError, ZodSchema } from 'zod';
import { APIRequest } from '../../types/api';
import { ValidationError } from '../../utils/errors/validation.error';

// Validation target types
export type ValidationTarget = 'body' | 'query' | 'params' | 'headers' | 'files';

// Validation schema configuration
export interface ValidationConfig {
  body?: ZodSchema;
  query?: ZodSchema;
  params?: ZodSchema;
  headers?: ZodSchema;
  files?: ZodSchema;
}

// Validation options
export interface ValidationOptions {
  stripUnknown?: boolean; // Remove unknown fields
  abortEarly?: boolean; // Stop on first error
  allowUnknown?: boolean; // Allow unknown fields
}

// Transform Zod errors to API format
const transformZodError = (error: ZodError): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    errors[path] = err.message;
  });
  
  return errors;
};

// Main validation middleware factory
export const validate = (config: ValidationConfig, options: ValidationOptions = {}) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      const validationPromises: Promise<any>[] = [];
      const validationResults: Record<string, any> = {};

      // Validate body
      if (config.body) {
        validationPromises.push(
          config.body.parseAsync(req.body).then(result => {
            validationResults.body = result;
          })
        );
      }

      // Validate query
      if (config.query) {
        validationPromises.push(
          config.query.parseAsync(req.query).then(result => {
            validationResults.query = result;
          })
        );
      }

      // Validate params
      if (config.params) {
        validationPromises.push(
          config.params.parseAsync(req.params).then(result => {
            validationResults.params = result;
          })
        );
      }

      // Validate headers
      if (config.headers) {
        validationPromises.push(
          config.headers.parseAsync(req.headers).then(result => {
            validationResults.headers = result;
          })
        );
      }

      // Validate files
      if (config.files && req.files) {
        validationPromises.push(
          config.files.parseAsync(req.files).then(result => {
            validationResults.files = result;
          })
        );
      }

      // Wait for all validations to complete
      await Promise.all(validationPromises);

      // Update request with validated data
      if (validationResults.body) req.body = validationResults.body;
      if (validationResults.query) req.query = validationResults.query;
      if (validationResults.params) req.params = validationResults.params;
      if (validationResults.headers) {
        // Only update specific headers, not all
        Object.assign(req.headers, validationResults.headers);
      }
      if (validationResults.files) req.files = validationResults.files;

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = transformZodError(error);
        return next(new ValidationError(validationErrors));
      }
      next(error);
    }
  };
};

// Shorthand validation functions
export const validateBody = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate({ body: schema }, options);
};

export const validateQuery = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate({ query: schema }, options);
};

export const validateParams = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate({ params: schema }, options);
};

export const validateHeaders = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate({ headers: schema }, options);
};

export const validateFiles = (schema: ZodSchema, options?: ValidationOptions) => {
  return validate({ files: schema }, options);
};

// Combined validation for common patterns
export const validateRequest = (schema: ZodSchema, options?: ValidationOptions) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      const validatedData = await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
        headers: req.headers,
        files: req.files,
      });

      // Update request with validated data
      if (validatedData.body) req.body = validatedData.body;
      if (validatedData.query) req.query = validatedData.query;
      if (validatedData.params) req.params = validatedData.params;
      if (validatedData.headers) {
        Object.assign(req.headers, validatedData.headers);
      }
      if (validatedData.files) req.files = validatedData.files;

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = transformZodError(error);
        return next(new ValidationError(validationErrors));
      }
      next(error);
    }
  };
};

// Async validation wrapper
export const asyncValidate = (validationFn: (req: APIRequest) => Promise<void>) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      await validationFn(req);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = transformZodError(error);
        return next(new ValidationError(validationErrors));
      }
      next(error);
    }
  };
};

// Conditional validation
export const validateIf = (
  condition: (req: APIRequest) => boolean,
  schema: ZodSchema,
  options?: ValidationOptions
) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    if (!condition(req)) {
      return next();
    }
    
    return validate({ body: schema }, options)(req, res, next);
  };
};

// Array validation helper
export const validateArray = (itemSchema: ZodSchema, minItems = 0, maxItems = 100) => {
  return z.array(itemSchema).min(minItems).max(maxItems);
};

// Optional field validation
export const validateOptional = (schema: ZodSchema) => {
  return schema.optional();
};

// Transform and validate
export const transformAndValidate = <T>(
  schema: ZodSchema<T>,
  transformer: (data: any) => any
) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      const transformedData = transformer(req.body);
      const validatedData = await schema.parseAsync(transformedData);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors = transformZodError(error);
        return next(new ValidationError(validationErrors));
      }
      next(error);
    }
  };
};

// Validation with custom error messages
export const validateWithCustomErrors = (
  schema: ZodSchema,
  errorMap: (error: ZodError) => Record<string, string>
) => {
  return async (req: APIRequest, res: Response, next: NextFunction) => {
    try {
      const validatedData = await schema.parseAsync(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const customErrors = errorMap(error);
        return next(new ValidationError(customErrors));
      }
      next(error);
    }
  };
};

// Validation result type
export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

// Manual validation function (for use in services)
export const validateData = async <T>(
  data: unknown,
  schema: ZodSchema<T>
): Promise<ValidationResult<T>> => {
  try {
    const validatedData = await schema.parseAsync(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        errors: transformZodError(error),
      };
    }
    throw error;
  }
};

// Export commonly used schemas
export * from '../validators/common/base.validator';
export * from '../validators/website/game.validator';
export * from '../validators/website/banner.validator';
export * from '../validators/website/tracking.validator';
