import { z } from 'zod';
import {
  fidSchema,
  uidSchema,
  paginationSchema,
  baseQuerySchema,
  paramsWithFidSchema,
  paramsWithCodeSchema,
  createArraySchema,
  shortTextSchema,
} from '../common/base.validator';

// Game code schema
export const gameCodeSchema = z.string()
  .min(1, 'Game code is required')
  .max(100, 'Game code is too long')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Game code can only contain letters, numbers, underscores, and hyphens');

// Game name schema
export const gameNameSchema = shortTextSchema.min(1, 'Game name is required');

// Game item schema
export const gameItemSchema = z.object({
  code: gameCodeSchema,
  name: gameNameSchema,
});

// Create game request
export const createGameSchema = z.object({
  body: z.object({
    fid: fidSchema,
    data: createArraySchema(gameItemSchema, 1, 50),
  }),
});

// Get games list query
export const getGamesListSchema = z.object({
  query: baseQuerySchema.extend({
    fid: fidSchema.optional(),
    category: z.string().optional(),
    trending: z.enum(['0', '1']).optional(),
  }),
});

// Get top games params
export const getTopGamesSchema = z.object({
  params: paramsWithFidSchema,
  query: paginationSchema.extend({
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
  }),
});

// Get user games params
export const getUserGamesSchema = z.object({
  params: paramsWithFidSchema.extend({
    uid: uidSchema,
  }),
  query: paginationSchema.extend({
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
  }),
});

// Get game detail params
export const getGameDetailSchema = z.object({
  params: paramsWithCodeSchema,
});

// Open game request
export const openGameSchema = z.object({
  body: z.object({
    code: gameCodeSchema,
    fid: fidSchema,
    uid: uidSchema.optional(),
    metadata: z.record(z.any()).optional(),
  }),
});

// RSS request
export const rssRequestSchema = z.object({
  query: z.object({
    link: z.string().url('Invalid RSS URL'),
  }),
});

// Game report schemas
export const gameReportFilterSchema = z.object({
  query: z.object({
    fid: fidSchema.optional(),
    code: gameCodeSchema.optional(),
    month: z.coerce.number().int().min(1).max(12).optional(),
    year: z.coerce.number().int().min(2020).max(2030).optional(),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    ...paginationSchema.shape,
  }),
});

// Update game report (for queue processing)
export const updateGameReportSchema = z.object({
  fid: fidSchema,
  code: gameCodeSchema,
  uid: uidSchema.optional(),
  month: z.number().int().min(1).max(12),
  year: z.number().int().min(2020).max(2030),
});

// Game statistics request
export const gameStatsSchema = z.object({
  query: z.object({
    fid: fidSchema.optional(),
    codes: z.string().transform((val) => val.split(',')).pipe(z.array(gameCodeSchema)).optional(),
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
    groupBy: z.enum(['code', 'fid', 'date']).default('code'),
    ...paginationSchema.shape,
  }),
});

// Bulk game operations
export const bulkGameOperationSchema = z.object({
  body: z.object({
    operation: z.enum(['activate', 'deactivate', 'delete']),
    codes: createArraySchema(gameCodeSchema, 1, 100),
    reason: z.string().max(500).optional(),
  }),
});

// Game search schema
export const gameSearchSchema = z.object({
  query: z.object({
    q: z.string().min(1, 'Search query is required').max(100),
    fid: fidSchema.optional(),
    category: z.string().optional(),
    status: z.enum(['0', '1']).optional(),
    sortBy: z.enum(['name', 'code', 'created_at', 'popularity']).default('name'),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
    ...paginationSchema.shape,
  }),
});

// Game category schemas
export const gameCategorySchema = z.object({
  body: z.object({
    name: gameNameSchema,
    slug: z.string().regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
    description: z.string().max(500).optional(),
    icon: z.string().url().optional(),
    sort: z.number().int().min(0).optional(),
    status: z.enum([0, 1]).default(1),
  }),
});

// Game metadata schema
export const gameMetadataSchema = z.object({
  body: z.object({
    code: gameCodeSchema,
    metadata: z.object({
      description: z.string().max(1000).optional(),
      tags: z.array(z.string().max(50)).max(10).optional(),
      difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
      minPlayers: z.number().int().min(1).optional(),
      maxPlayers: z.number().int().min(1).optional(),
      estimatedTime: z.number().int().min(1).optional(), // in minutes
      ageRating: z.enum(['all', '7+', '12+', '16+', '18+']).optional(),
      genre: z.array(z.string().max(50)).max(5).optional(),
      platform: z.array(z.enum(['web', 'mobile', 'desktop'])).optional(),
      features: z.array(z.string().max(100)).max(10).optional(),
    }),
  }),
});

// Export types
export type CreateGameRequest = z.infer<typeof createGameSchema>;
export type GetGamesListRequest = z.infer<typeof getGamesListSchema>;
export type GetTopGamesRequest = z.infer<typeof getTopGamesSchema>;
export type GetUserGamesRequest = z.infer<typeof getUserGamesSchema>;
export type GetGameDetailRequest = z.infer<typeof getGameDetailSchema>;
export type OpenGameRequest = z.infer<typeof openGameSchema>;
export type RssRequest = z.infer<typeof rssRequestSchema>;
export type GameReportFilterRequest = z.infer<typeof gameReportFilterSchema>;
export type UpdateGameReportData = z.infer<typeof updateGameReportSchema>;
export type GameStatsRequest = z.infer<typeof gameStatsSchema>;
export type BulkGameOperationRequest = z.infer<typeof bulkGameOperationSchema>;
export type GameSearchRequest = z.infer<typeof gameSearchSchema>;
export type GameCategoryRequest = z.infer<typeof gameCategorySchema>;
export type GameMetadataRequest = z.infer<typeof gameMetadataSchema>;
