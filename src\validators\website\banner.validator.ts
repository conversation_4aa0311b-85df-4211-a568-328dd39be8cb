import { z } from 'zod';
import {
  idSchema,
  fidSchema,
  paginationSchema,
  baseQuerySchema,
  paramsWithIdSchema,
  urlSchema,
  shortTextSchema,
  mediumTextSchema,
  dateStringSchema,
  statusSchema,
} from '../common/base.validator';

// Banner position schema
export const bannerPositionSchema = z.object({
  id: idSchema,
  name: shortTextSchema,
  code: z.string().regex(/^[a-z0-9_-]+$/, 'Position code can only contain lowercase letters, numbers, underscores, and hyphens'),
  width: z.number().int().positive().optional(),
  height: z.number().int().positive().optional(),
});

// Banner image schema
export const bannerImageSchema = z.string()
  .url('Invalid image URL')
  .regex(/\.(jpg|jpeg|png|gif|webp)$/i, 'Image must be a valid image file');

// Banner link schema
export const bannerLinkSchema = urlSchema.optional().or(z.literal(''));

// Banner rank schema
export const bannerRankSchema = z.number().int().min(1).max(100).optional();

// Banner ratio schema
export const bannerRatioSchema = z.number().int().min(1).max(100).optional();

// Create banner request
export const createBannerSchema = z.object({
  body: z.object({
    fid: fidSchema,
    name: shortTextSchema.optional(),
    image: bannerImageSchema,
    link: bannerLinkSchema,
    positionId: idSchema,
    rank: bannerRankSchema,
    ratio: bannerRatioSchema,
    status: statusSchema.default(1),
  }),
});

// Update banner request
export const updateBannerSchema = z.object({
  params: paramsWithIdSchema,
  body: z.object({
    name: shortTextSchema.optional(),
    image: bannerImageSchema.optional(),
    link: bannerLinkSchema,
    positionId: idSchema.optional(),
    rank: bannerRankSchema,
    ratio: bannerRatioSchema,
    status: statusSchema.optional(),
  }),
});

// Get banners list query
export const getBannersListSchema = z.object({
  query: baseQuerySchema.extend({
    fid: fidSchema.optional(),
    positionId: idSchema.optional(),
    rank: bannerRankSchema,
  }),
});

// Get banner detail params
export const getBannerDetailSchema = z.object({
  params: paramsWithIdSchema,
});

// Banner V2 schemas
export const createBannerV2Schema = z.object({
  body: z.object({
    region: z.string().max(500).optional(),
    city: z.string().max(500).optional(),
    fid: z.string().max(500).optional(),
    positionId: idSchema,
    clientId: idSchema,
    clientProductId: idSchema,
    image: bannerImageSchema,
    applyDate: z.string().max(255).optional(),
    applyStartDate: z.string().datetime().optional(),
    applyEndDate: z.string().datetime().optional(),
    status: statusSchema.default(1),
  }),
});

export const updateBannerV2Schema = z.object({
  params: paramsWithIdSchema,
  body: z.object({
    region: z.string().max(500).optional(),
    city: z.string().max(500).optional(),
    fid: z.string().max(500).optional(),
    positionId: idSchema.optional(),
    clientId: idSchema.optional(),
    clientProductId: idSchema.optional(),
    image: bannerImageSchema.optional(),
    applyDate: z.string().max(255).optional(),
    applyStartDate: z.string().datetime().optional(),
    applyEndDate: z.string().datetime().optional(),
    status: statusSchema.optional(),
  }),
});

export const getBannersV2ListSchema = z.object({
  query: baseQuerySchema.extend({
    region: z.string().optional(),
    city: z.string().optional(),
    fid: fidSchema.optional(),
    positionId: idSchema.optional(),
    clientId: idSchema.optional(),
    clientProductId: idSchema.optional(),
    dateFrom: dateStringSchema.optional(),
    dateTo: dateStringSchema.optional(),
    active: z.enum(['0', '1']).optional(), // Filter by current date range
  }),
});

// Banner time slot schemas
export const timeSlotSchema = z.object({
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/, 'Invalid time format (HH:MM:SS)'),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/, 'Invalid time format (HH:MM:SS)'),
  dayOfWeek: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']).optional(),
});

export const createBannerTimeSlotSchema = z.object({
  body: z.object({
    bannerId: idSchema,
    timeSlots: z.array(timeSlotSchema).min(1, 'At least one time slot is required').max(10),
  }),
});

// Banner position schemas
export const createBannerPositionSchema = z.object({
  body: z.object({
    name: shortTextSchema,
    code: z.string().regex(/^[a-z0-9_-]+$/, 'Position code can only contain lowercase letters, numbers, underscores, and hyphens'),
    description: mediumTextSchema.optional(),
    width: z.number().int().positive().optional(),
    height: z.number().int().positive().optional(),
    status: statusSchema.default(1),
  }),
});

export const updateBannerPositionSchema = z.object({
  params: paramsWithIdSchema,
  body: z.object({
    name: shortTextSchema.optional(),
    description: mediumTextSchema.optional(),
    width: z.number().int().positive().optional(),
    height: z.number().int().positive().optional(),
    status: statusSchema.optional(),
  }),
});

// Banner report schemas
export const bannerReportFilterSchema = z.object({
  query: z.object({
    bannerId: idSchema.optional(),
    fid: fidSchema.optional(),
    type: z.coerce.number().int().min(1).max(10).optional(),
    month: z.coerce.number().int().min(1).max(12).optional(),
    year: z.coerce.number().int().min(2020).max(2030).optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    ...paginationSchema.shape,
  }),
});

export const updateBannerReportSchema = z.object({
  bannerId: idSchema,
  fid: fidSchema,
  type: z.number().int().min(1).max(10),
  currentDate: dateStringSchema,
});

// Banner analytics schemas
export const bannerAnalyticsSchema = z.object({
  query: z.object({
    bannerIds: z.string().transform((val) => val.split(',')).pipe(z.array(idSchema)).optional(),
    fid: fidSchema.optional(),
    positionId: idSchema.optional(),
    period: z.enum(['day', 'week', 'month', 'year']).default('month'),
    groupBy: z.enum(['banner', 'position', 'fid', 'date']).default('banner'),
    metrics: z.string().transform((val) => val.split(',')).pipe(
      z.array(z.enum(['clicks', 'impressions', 'ctr', 'unique_clicks']))
    ).default(['clicks']),
    ...paginationSchema.shape,
  }),
});

// Banner bulk operations
export const bulkBannerOperationSchema = z.object({
  body: z.object({
    operation: z.enum(['activate', 'deactivate', 'delete', 'change_position']),
    bannerIds: z.array(idSchema).min(1, 'At least one banner ID is required').max(100),
    newPositionId: idSchema.optional(), // Required when operation is 'change_position'
    reason: z.string().max(500).optional(),
  }),
});

// Banner search schema
export const bannerSearchSchema = z.object({
  query: z.object({
    q: z.string().min(1, 'Search query is required').max(100),
    fid: fidSchema.optional(),
    positionId: idSchema.optional(),
    status: z.enum(['0', '1']).optional(),
    sortBy: z.enum(['name', 'created_at', 'rank', 'clicks']).default('created_at'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    ...paginationSchema.shape,
  }),
});

// Export types
export type CreateBannerRequest = z.infer<typeof createBannerSchema>;
export type UpdateBannerRequest = z.infer<typeof updateBannerSchema>;
export type GetBannersListRequest = z.infer<typeof getBannersListSchema>;
export type GetBannerDetailRequest = z.infer<typeof getBannerDetailSchema>;
export type CreateBannerV2Request = z.infer<typeof createBannerV2Schema>;
export type UpdateBannerV2Request = z.infer<typeof updateBannerV2Schema>;
export type GetBannersV2ListRequest = z.infer<typeof getBannersV2ListSchema>;
export type CreateBannerTimeSlotRequest = z.infer<typeof createBannerTimeSlotSchema>;
export type CreateBannerPositionRequest = z.infer<typeof createBannerPositionSchema>;
export type UpdateBannerPositionRequest = z.infer<typeof updateBannerPositionSchema>;
export type BannerReportFilterRequest = z.infer<typeof bannerReportFilterSchema>;
export type UpdateBannerReportData = z.infer<typeof updateBannerReportSchema>;
export type BannerAnalyticsRequest = z.infer<typeof bannerAnalyticsSchema>;
export type BulkBannerOperationRequest = z.infer<typeof bulkBannerOperationSchema>;
export type BannerSearchRequest = z.infer<typeof bannerSearchSchema>;
