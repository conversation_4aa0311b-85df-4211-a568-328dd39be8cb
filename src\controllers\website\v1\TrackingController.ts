import { Response, NextFunction } from 'express';
import { BaseController } from '../../base/BaseController';
import { TrackingService } from '../../../services/website/TrackingService';
import { APIRequest } from '../../../types/api';
import {
  TrackLinkRequest,
  TrackBannerRequest,
  TrackVideoRequest,
  TrackGameRequest,
  TrackPageViewRequest,
  TrackEventRequest,
  BulkTrackingRequest,
} from '../../../validators/website/tracking.validator';

export class TrackingController extends BaseController {
  private trackingService: TrackingService;

  constructor(trackingService: TrackingService) {
    super(trackingService);
    this.trackingService = trackingService;
  }

  // POST /tracking/link - Track link click
  openLink = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'openLink');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackLinkRequest;
        return await this.trackingService.trackLink(body, req.ip, req.get('User-Agent'));
      },
      'Link tracking recorded successfully'
    );
  });

  // POST /tracking/banner - Track banner click
  openBanner = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'openBanner');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackBannerRequest;
        return await this.trackingService.trackBanner(body, req.ip, req.get('User-Agent'));
      },
      'Banner tracking recorded successfully'
    );
  });

  // POST /tracking/video - Track video interaction
  openVideo = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'openVideo');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackVideoRequest;
        return await this.trackingService.trackVideo(body, req.ip, req.get('User-Agent'));
      },
      'Video tracking recorded successfully'
    );
  });

  // POST /tracking/game - Track game interaction
  trackGame = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'trackGame');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackGameRequest;
        return await this.trackingService.trackGame(body, req.ip, req.get('User-Agent'));
      },
      'Game tracking recorded successfully'
    );
  });

  // POST /tracking/page-view - Track page view
  trackPageView = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'trackPageView');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackPageViewRequest;
        return await this.trackingService.trackPageView(body, req.ip, req.get('User-Agent'));
      },
      'Page view tracking recorded successfully'
    );
  });

  // POST /tracking/event - Track custom event
  trackEvent = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'trackEvent');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as TrackEventRequest;
        return await this.trackingService.trackEvent(body, req.ip, req.get('User-Agent'));
      },
      'Event tracking recorded successfully'
    );
  });

  // POST /tracking/bulk - Bulk tracking
  bulkTracking = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'bulkTracking');

    await this.handleBulkRequest(
      req,
      res,
      next,
      async (req) => {
        const { body } = req as BulkTrackingRequest;
        return await this.trackingService.bulkTracking(body, req.ip, req.get('User-Agent'));
      },
      'Bulk tracking recorded successfully'
    );
  });

  // GET /tracking/analytics - Get analytics dashboard
  getAnalyticsDashboard = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getAnalyticsDashboard');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'period', 'startDate', 'endDate', 'metrics', 'groupBy']);
        return await this.trackingService.getAnalyticsDashboard(filters);
      },
      'Analytics dashboard data retrieved successfully'
    );
  });

  // GET /tracking/realtime - Get real-time analytics
  getRealTimeAnalytics = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getRealTimeAnalytics');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'limit', 'eventTypes']);
        return await this.trackingService.getRealTimeAnalytics(filters);
      },
      'Real-time analytics retrieved successfully'
    );
  });

  // GET /tracking/reports/links - Get link reports
  getLinkReports = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getLinkReports');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['link', 'type', 'fid', 'month', 'year', 'startDate', 'endDate']);
        const pagination = this.getPaginationParams(req);
        return await this.trackingService.getLinkReports(filters, pagination);
      },
      'Link reports retrieved successfully'
    );
  });

  // GET /tracking/reports/banners - Get banner reports
  getBannerReports = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerReports');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['bannerId', 'type', 'fid', 'month', 'year', 'startDate', 'endDate']);
        const pagination = this.getPaginationParams(req);
        return await this.trackingService.getBannerReports(filters, pagination);
      },
      'Banner reports retrieved successfully'
    );
  });

  // GET /tracking/reports/videos - Get video reports
  getVideoReports = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getVideoReports');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['videoId', 'playlistId', 'type', 'fid', 'month', 'year', 'startDate', 'endDate']);
        const pagination = this.getPaginationParams(req);
        return await this.trackingService.getVideoReports(filters, pagination);
      },
      'Video reports retrieved successfully'
    );
  });

  // POST /tracking/conversion - Track conversion
  trackConversion = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'trackConversion');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        return await this.trackingService.trackConversion(req.body, req.ip, req.get('User-Agent'));
      },
      'Conversion tracking recorded successfully'
    );
  });

  // POST /tracking/ab-test - Track A/B test
  trackABTest = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'trackABTest');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        return await this.trackingService.trackABTest(req.body, req.ip, req.get('User-Agent'));
      },
      'A/B test tracking recorded successfully'
    );
  });

  // GET /tracking/heatmap - Get heatmap data
  getHeatmapData = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getHeatmapData');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['page', 'fid', 'startDate', 'endDate', 'deviceType', 'resolution']);
        return await this.trackingService.getHeatmapData(filters);
      },
      'Heatmap data retrieved successfully'
    );
  });

  // GET /tracking/funnel - Get funnel analysis
  getFunnelAnalysis = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getFunnelAnalysis');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { steps, fid, period } = req.query;
        return await this.trackingService.getFunnelAnalysis(
          (steps as string).split(','),
          fid as string,
          period as string
        );
      },
      'Funnel analysis retrieved successfully'
    );
  });

  // GET /tracking/cohort - Get cohort analysis
  getCohortAnalysis = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getCohortAnalysis');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'period', 'cohortType']);
        return await this.trackingService.getCohortAnalysis(filters);
      },
      'Cohort analysis retrieved successfully'
    );
  });
}
