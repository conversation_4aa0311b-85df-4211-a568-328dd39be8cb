import { Response, NextFunction } from 'express';
import { BaseController } from '../../base/BaseController';
import { BannerService } from '../../../services/website/BannerService';
import { APIRequest } from '../../../types/api';
import {
  GetBannersListRequest,
  GetBannersV2ListRequest,
} from '../../../validators/website/banner.validator';

export class BannerController extends BaseController {
  private bannerService: BannerService;

  constructor(bannerService: BannerService) {
    super(bannerService);
    this.bannerService = bannerService;
  }

  // GET /banner - Get list of banners (v1)
  getListBanners = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListBanners');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const { query } = req as GetBannersListRequest;
        return await this.bannerService.getListBanners(query);
      },
      'Banners retrieved successfully'
    );
  });

  // GET /banner-v2 - Get list of banners (v2)
  getListBannersV2 = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getListBannersV2');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const { query } = req as GetBannersV2ListRequest;
        return await this.bannerService.getListBannersV2(query);
      },
      'Banners V2 retrieved successfully'
    );
  });

  // GET /banner/:id - Get banner detail
  getBannerDetail = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerDetail');

    await this.handleDetailRequest(
      req,
      res,
      next,
      async (req) => {
        const id = parseInt(req.params.id);
        return await this.bannerService.getBannerDetail(id);
      },
      'Banner detail retrieved successfully',
      'Banner not found'
    );
  });

  // POST /banner - Create banner
  createBanner = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'createBanner');

    await this.handleCreateRequest(
      req,
      res,
      next,
      async (req) => {
        return await this.bannerService.createBanner(req.body);
      },
      'Banner created successfully'
    );
  });

  // PUT /banner/:id - Update banner
  updateBanner = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'updateBanner');

    await this.handleUpdateRequest(
      req,
      res,
      next,
      async (req) => {
        const id = parseInt(req.params.id);
        return await this.bannerService.updateBanner(id, req.body);
      },
      'Banner updated successfully',
      'Banner not found'
    );
  });

  // DELETE /banner/:id - Delete banner
  deleteBanner = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'deleteBanner');

    await this.handleDeleteRequest(
      req,
      res,
      next,
      async (req) => {
        const id = parseInt(req.params.id);
        return await this.bannerService.deleteBanner(id);
      },
      'Banner deleted successfully',
      'Banner not found'
    );
  });

  // GET /banner/analytics - Get banner analytics
  getBannerAnalytics = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerAnalytics');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['bannerIds', 'fid', 'positionId', 'period', 'groupBy', 'metrics']);
        const pagination = this.getPaginationParams(req);
        return await this.bannerService.getBannerAnalytics(filters, pagination);
      },
      'Banner analytics retrieved successfully'
    );
  });

  // POST /banner/bulk - Bulk banner operations
  bulkBannerOperation = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'bulkBannerOperation');

    await this.handleBulkRequest(
      req,
      res,
      next,
      async (req) => {
        const { operation, bannerIds, newPositionId, reason } = req.body;
        return await this.bannerService.bulkBannerOperation(operation, bannerIds, newPositionId, reason);
      },
      'Bulk banner operation completed'
    );
  });

  // GET /banner/search - Search banners
  searchBanners = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'searchBanners');

    await this.handlePaginatedRequest(
      req,
      res,
      next,
      async (req) => {
        const { q, ...filters } = req.query;
        const pagination = this.getPaginationParams(req);
        const sort = this.getSortParams(req, ['name', 'created_at', 'rank', 'clicks'], 'created_at');
        
        return await this.bannerService.searchBanners(q as string, filters, pagination, sort);
      },
      'Banner search results retrieved successfully'
    );
  });

  // GET /banner/positions - Get banner positions
  getBannerPositions = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerPositions');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['status']);
        return await this.bannerService.getBannerPositions(filters);
      },
      'Banner positions retrieved successfully'
    );
  });

  // GET /banner/stats - Get banner statistics
  getBannerStats = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerStats');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const filters = this.getFilterParams(req, ['fid', 'positionId', 'period']);
        return await this.bannerService.getBannerStats(filters);
      },
      'Banner statistics retrieved successfully'
    );
  });

  // GET /banner/trending - Get trending banners
  getTrendingBanners = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getTrendingBanners');

    await this.handleListRequest(
      req,
      res,
      next,
      async (req) => {
        const { limit = 10, period = 'week' } = req.query;
        return await this.bannerService.getTrendingBanners(Number(limit), period as string);
      },
      'Trending banners retrieved successfully'
    );
  });

  // POST /banner/schedule - Schedule banner
  scheduleBanner = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'scheduleBanner');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const { bannerId, schedule } = req.body;
        return await this.bannerService.scheduleBanner(bannerId, schedule);
      },
      'Banner scheduled successfully'
    );
  });

  // GET /banner/:id/performance - Get banner performance
  getBannerPerformance = this.asyncHandler(async (req: APIRequest, res: Response, next: NextFunction) => {
    this.logRequest(req, 'getBannerPerformance');

    await this.handleRequest(
      req,
      res,
      next,
      async (req) => {
        const id = parseInt(req.params.id);
        const { period = 'month', metrics = 'clicks,impressions' } = req.query;
        return await this.bannerService.getBannerPerformance(id, period as string, (metrics as string).split(','));
      },
      'Banner performance retrieved successfully'
    );
  });
}
