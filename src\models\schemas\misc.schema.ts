import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const apiVersions = mysqlTable('api_versions', {
  id: int('id').primaryKey().autoincrement(),
  client: varchar('client', { length: 255 }).notNull().unique(),
  api: int('api').notNull(),
  description: text('description'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  clientIdx: index('idx_api_versions_client').on(table.client),
  apiIdx: index('idx_api_versions_api').on(table.api),
  statusIdx: index('idx_api_versions_status').on(table.status),
}));

export const clients = mysqlTable('clients', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 100 }).notNull().unique(),
  description: text('description'),
  logo: varchar('logo', { length: 500 }),
  website: varchar('website', { length: 500 }),
  contactEmail: varchar('contact_email', { length: 255 }),
  contactPhone: varchar('contact_phone', { length: 20 }),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  codeIdx: index('idx_clients_code').on(table.code),
  statusIdx: index('idx_clients_status').on(table.status),
}));

export const clientProducts = mysqlTable('client_products', {
  id: int('id').primaryKey().autoincrement(),
  clientId: int('client_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 100 }).notNull(),
  description: text('description'),
  logo: varchar('logo', { length: 500 }),
  url: varchar('url', { length: 500 }),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  clientIdx: index('idx_client_products_client').on(table.clientId),
  codeIdx: index('idx_client_products_code').on(table.code),
  statusIdx: index('idx_client_products_status').on(table.status),
}));

export const bannerPositions = mysqlTable('banner_positions', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 100 }).notNull().unique(),
  description: text('description'),
  width: int('width'),
  height: int('height'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  codeIdx: index('idx_banner_positions_code').on(table.code),
  statusIdx: index('idx_banner_positions_status').on(table.status),
}));

export const bannerTimeSlots = mysqlTable('banner_time_slots', {
  id: int('id').primaryKey().autoincrement(),
  bannerId: int('banner_id').notNull(),
  startTime: varchar('start_time', { length: 8 }).notNull(), // HH:MM:SS
  endTime: varchar('end_time', { length: 8 }).notNull(), // HH:MM:SS
  dayOfWeek: varchar('day_of_week', { length: 20 }), // monday,tuesday,etc or null for all days
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  bannerIdx: index('idx_banner_time_slots_banner').on(table.bannerId),
  timeIdx: index('idx_banner_time_slots_time').on(table.startTime, table.endTime),
  dayIdx: index('idx_banner_time_slots_day').on(table.dayOfWeek),
  statusIdx: index('idx_banner_time_slots_status').on(table.status),
}));

export const feedbacks = mysqlTable('feedbacks', {
  id: int('id').primaryKey().autoincrement(),
  fid: varchar('fid', { length: 255 }).notNull(),
  uid: varchar('uid', { length: 255 }).notNull(),
  pcn: varchar('pcn', { length: 255 }).notNull(),
  feedbackId: varchar('feedback_id', { length: 255 }).notNull(),
  feedbackTitle: varchar('feedback_title', { length: 500 }).notNull(),
  content: text('content'),
  status: int('status').notNull().default(0), // 0: pending, 1: processed
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  fidIdx: index('idx_feedbacks_fid').on(table.fid),
  uidIdx: index('idx_feedbacks_uid').on(table.uid),
  statusIdx: index('idx_feedbacks_status').on(table.status),
  createdAtIdx: index('idx_feedbacks_created_at').on(table.createdAt),
}));

export const flashNews = mysqlTable('flash_news', {
  id: int('id').primaryKey().autoincrement(),
  title: varchar('title', { length: 500 }).notNull(),
  content: text('content').notNull(),
  link: varchar('link', { length: 500 }),
  image: varchar('image', { length: 500 }),
  publishDate: timestamp('publish_date').notNull(),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  statusIdx: index('idx_flash_news_status').on(table.status),
  publishDateIdx: index('idx_flash_news_publish_date').on(table.publishDate),
}));

export const rssLinks = mysqlTable('rss_links', {
  id: int('id').primaryKey().autoincrement(),
  url: varchar('url', { length: 500 }).notNull().unique(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  lastFetched: timestamp('last_fetched'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  urlIdx: index('idx_rss_links_url').on(table.url),
  statusIdx: index('idx_rss_links_status').on(table.status),
  lastFetchedIdx: index('idx_rss_links_last_fetched').on(table.lastFetched),
}));

export const events = mysqlTable('events', {
  id: int('id').primaryKey().autoincrement(),
  fid: varchar('fid', { length: 255 }).notNull(),
  uid: varchar('uid', { length: 255 }),
  pcn: varchar('pcn', { length: 255 }),
  version: varchar('version', { length: 50 }),
  eventType: varchar('event_type', { length: 100 }).notNull(),
  eventData: text('event_data'), // JSON string
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  fidIdx: index('idx_events_fid').on(table.fid),
  uidIdx: index('idx_events_uid').on(table.uid),
  eventTypeIdx: index('idx_events_event_type').on(table.eventType),
  createdAtIdx: index('idx_events_created_at').on(table.createdAt),
}));

export type ApiVersion = typeof apiVersions.$inferSelect;
export type NewApiVersion = typeof apiVersions.$inferInsert;
export type Client = typeof clients.$inferSelect;
export type NewClient = typeof clients.$inferInsert;
export type ClientProduct = typeof clientProducts.$inferSelect;
export type NewClientProduct = typeof clientProducts.$inferInsert;
export type BannerPosition = typeof bannerPositions.$inferSelect;
export type NewBannerPosition = typeof bannerPositions.$inferInsert;
export type BannerTimeSlot = typeof bannerTimeSlots.$inferSelect;
export type NewBannerTimeSlot = typeof bannerTimeSlots.$inferInsert;
export type Feedback = typeof feedbacks.$inferSelect;
export type NewFeedback = typeof feedbacks.$inferInsert;
export type FlashNews = typeof flashNews.$inferSelect;
export type NewFlashNews = typeof flashNews.$inferInsert;
export type RssLink = typeof rssLinks.$inferSelect;
export type NewRssLink = typeof rssLinks.$inferInsert;
export type Event = typeof events.$inferSelect;
export type NewEvent = typeof events.$inferInsert;
