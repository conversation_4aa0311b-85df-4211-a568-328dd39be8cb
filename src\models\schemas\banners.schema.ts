import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const banners = mysqlTable('banners', {
  id: int('id').primaryKey().autoincrement(),
  fid: varchar('fid', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }),
  image: varchar('image', { length: 500 }).notNull(),
  link: text('link'),
  positionId: int('position_id').notNull(),
  rank: int('rank'),
  ratio: int('ratio'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  fidIdx: index('idx_banners_fid').on(table.fid),
  positionIdx: index('idx_banners_position').on(table.positionId),
  statusIdx: index('idx_banners_status').on(table.status),
}));

export const bannersV2 = mysqlTable('banners_v2', {
  id: int('id').primaryKey().autoincrement(),
  region: text('region'),
  city: text('city'),
  fid: text('fid'),
  positionId: int('position_id').notNull(),
  clientId: int('client_id').notNull(),
  clientProductId: int('client_product_id').notNull(),
  image: varchar('image', { length: 500 }).notNull(),
  applyDate: varchar('apply_date', { length: 255 }),
  applyStartDate: timestamp('apply_start_date'),
  applyEndDate: timestamp('apply_end_date'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  positionIdx: index('idx_banners_v2_position').on(table.positionId),
  clientIdx: index('idx_banners_v2_client').on(table.clientId),
  statusIdx: index('idx_banners_v2_status').on(table.status),
  dateRangeIdx: index('idx_banners_v2_date_range').on(table.applyStartDate, table.applyEndDate),
}));

export type Banner = typeof banners.$inferSelect;
export type NewBanner = typeof banners.$inferInsert;
export type BannerV2 = typeof bannersV2.$inferSelect;
export type NewBannerV2 = typeof bannersV2.$inferInsert;
