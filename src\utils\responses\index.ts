import { Response } from 'express';

export class SuccessResponse {
  private status: string = 'success';
  private code: number;
  private message: string;
  private metadata?: any;

  constructor(metadata?: any, message: string = 'Operation successful', code: number = 200) {
    this.code = code;
    this.message = message;
    this.metadata = metadata;
  }

  send(res: Response): Response {
    const response: any = {
      status: this.status,
      code: this.code,
      message: this.message,
    };

    if (this.metadata !== undefined && this.metadata !== null) {
      response.metadata = this.metadata;
    }

    return res.status(this.code).json(response);
  }
}

export class ErrorResponse {
  private status: string = 'error';
  private code: number;
  private message: string;
  private errors?: Record<string, string>;

  constructor(message: string, code: number = 500, errors?: Record<string, string>) {
    this.code = code;
    this.message = message;
    this.errors = errors;
  }

  send(res: Response): Response {
    const response: any = {
      status: this.status,
      code: this.code,
      message: this.message,
    };

    if (this.errors) {
      response.errors = this.errors;
    }

    return res.status(this.code).json(response);
  }
}

// Utility functions for common responses
export const successResponse = (res: Response, data?: any, message?: string, code?: number) => {
  return new SuccessResponse(data, message, code).send(res);
};

export const errorResponse = (res: Response, message: string, code?: number, errors?: Record<string, string>) => {
  return new ErrorResponse(message, code, errors).send(res);
};

// Specific response types
export const createdResponse = (res: Response, data?: any, message: string = 'Resource created successfully') => {
  return new SuccessResponse(data, message, 201).send(res);
};

export const noContentResponse = (res: Response, message: string = 'Operation completed successfully') => {
  return new SuccessResponse(null, message, 204).send(res);
};

export const notFoundResponse = (res: Response, message: string = 'Resource not found') => {
  return new ErrorResponse(message, 404).send(res);
};

export const badRequestResponse = (res: Response, message: string = 'Bad request', errors?: Record<string, string>) => {
  return new ErrorResponse(message, 400, errors).send(res);
};

export const unauthorizedResponse = (res: Response, message: string = 'Unauthorized') => {
  return new ErrorResponse(message, 401).send(res);
};

export const forbiddenResponse = (res: Response, message: string = 'Forbidden') => {
  return new ErrorResponse(message, 403).send(res);
};

export const conflictResponse = (res: Response, message: string = 'Conflict') => {
  return new ErrorResponse(message, 409).send(res);
};

export const validationErrorResponse = (res: Response, errors: Record<string, string>, message: string = 'Validation failed') => {
  return new ErrorResponse(message, 400, errors).send(res);
};

export const internalServerErrorResponse = (res: Response, message: string = 'Internal server error') => {
  return new ErrorResponse(message, 500).send(res);
};
