{"name": "fus-cms-backend-v2", "version": "2.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "build": "tsc", "type-check": "tsc --noEmit"}, "author": "", "license": "ISC", "description": "FUS CMS Backend V2 - Refactored with MVC architecture", "dependencies": {"@bull-board/api": "^6.0.0", "@bull-board/express": "^6.0.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "bullmq": "^5.15.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.4", "express": "^4.21.0", "express-rate-limit": "^7.4.0", "helmet": "^7.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.45", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "node-telegram-bot-api": "^0.66.0", "rate-limit-redis": "^4.2.0", "rss-parser": "^3.13.0", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.9.0", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.28.1", "nodemon": "^3.1.7", "typescript": "^5.6.3"}, "engines": {"node": ">=18.0.0"}}