import { mysqlTable, int, varchar, text, timestamp, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const roles = mysqlTable('roles', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  description: text('description'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  nameIdx: index('idx_roles_name').on(table.name),
  statusIdx: index('idx_roles_status').on(table.status),
}));

export const modules = mysqlTable('modules', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  description: text('description'),
  icon: varchar('icon', { length: 255 }),
  sort: int('sort'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  nameIdx: index('idx_modules_name').on(table.name),
  statusIdx: index('idx_modules_status').on(table.status),
  sortIdx: index('idx_modules_sort').on(table.sort),
}));

export const permissions = mysqlTable('permissions', {
  id: int('id').primaryKey().autoincrement(),
  moduleId: int('module_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  action: varchar('action', { length: 100 }).notNull(),
  description: text('description'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  moduleIdx: index('idx_permissions_module').on(table.moduleId),
  actionIdx: index('idx_permissions_action').on(table.action),
  statusIdx: index('idx_permissions_status').on(table.status),
}));

export const rolePermissions = mysqlTable('role_permissions', {
  roleId: int('role_id').notNull(),
  permissionId: int('permission_id').notNull(),
}, (table) => ({
  roleIdx: index('idx_role_permissions_role').on(table.roleId),
  permissionIdx: index('idx_role_permissions_permission').on(table.permissionId),
  uniqueRolePermission: index('idx_role_permissions_unique').on(table.roleId, table.permissionId),
}));

export const admins = mysqlTable('admins', {
  id: int('id').primaryKey().autoincrement(),
  username: varchar('username', { length: 255 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }).notNull(),
  fullName: varchar('full_name', { length: 255 }),
  avatar: varchar('avatar', { length: 500 }),
  phone: varchar('phone', { length: 20 }),
  lastLoginAt: timestamp('last_login_at'),
  status: int('status').notNull().default(1),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: int('created_by'),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
  updatedBy: int('updated_by'),
}, (table) => ({
  usernameIdx: index('idx_admins_username').on(table.username),
  emailIdx: index('idx_admins_email').on(table.email),
  statusIdx: index('idx_admins_status').on(table.status),
}));

export const adminRoles = mysqlTable('admin_roles', {
  adminId: int('admin_id').notNull(),
  roleId: int('role_id').notNull(),
}, (table) => ({
  adminIdx: index('idx_admin_roles_admin').on(table.adminId),
  roleIdx: index('idx_admin_roles_role').on(table.roleId),
  uniqueAdminRole: index('idx_admin_roles_unique').on(table.adminId, table.roleId),
}));

export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;
export type Module = typeof modules.$inferSelect;
export type NewModule = typeof modules.$inferInsert;
export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;
export type RolePermission = typeof rolePermissions.$inferSelect;
export type NewRolePermission = typeof rolePermissions.$inferInsert;
export type Admin = typeof admins.$inferSelect;
export type NewAdmin = typeof admins.$inferInsert;
export type AdminRole = typeof adminRoles.$inferSelect;
export type NewAdminRole = typeof adminRoles.$inferInsert;
