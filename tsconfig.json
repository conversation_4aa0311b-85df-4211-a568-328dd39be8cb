{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/models/*": ["models/*"], "@/controllers/*": ["controllers/*"], "@/services/*": ["services/*"], "@/repositories/*": ["repositories/*"], "@/middlewares/*": ["middlewares/*"], "@/validators/*": ["validators/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*", "server.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}