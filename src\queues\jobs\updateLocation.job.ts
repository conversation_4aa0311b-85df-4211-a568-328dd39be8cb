import { Job } from 'bullmq';
import { db } from '../../config/database';
import { settings } from '../../models/schemas';
import { eq, and } from 'drizzle-orm';

// Job data interface
export interface UpdateLocationJobData {
  fid: string;
  fuid?: string;
  locationData?: {
    name?: string;
    address?: string;
    latitude?: number;
    longitude?: number;
    phone?: string;
    email?: string;
    website?: string;
    description?: string;
  };
}

// Job options
export const updateLocationJobOptions = {
  removeOnComplete: 10,
  removeOnFail: 5,
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
};

// Job processor
export const processUpdateLocation = async (job: Job<UpdateLocationJobData>): Promise<void> => {
  const { fid, fuid, locationData } = job.data;

  try {
    console.log(`Processing update location job: ${fid}/${fuid}`);

    // Update location settings
    if (locationData) {
      await updateLocationSettings(fid, locationData);
    }

    // Update location cache
    await updateLocationCache(fid, fuid, locationData);

    // Log location update event
    await logLocationUpdate(fid, fuid, locationData);

    console.log(`Update location job completed: ${fid}/${fuid}`);
  } catch (error) {
    console.error(`Update location job failed: ${fid}/${fuid}`, error);
    throw error;
  }
};

// Helper function to update location settings
const updateLocationSettings = async (
  fid: string,
  locationData: UpdateLocationJobData['locationData']
): Promise<void> => {
  try {
    if (!locationData) return;

    const settingsToUpdate = [
      { key: 'location_name', value: locationData.name },
      { key: 'location_address', value: locationData.address },
      { key: 'location_latitude', value: locationData.latitude?.toString() },
      { key: 'location_longitude', value: locationData.longitude?.toString() },
      { key: 'location_phone', value: locationData.phone },
      { key: 'location_email', value: locationData.email },
      { key: 'location_website', value: locationData.website },
      { key: 'location_description', value: locationData.description },
    ];

    for (const setting of settingsToUpdate) {
      if (setting.value !== undefined && setting.value !== null) {
        // Check if setting exists
        const existingSetting = await db
          .select()
          .from(settings)
          .where(
            and(
              eq(settings.fid, fid),
              eq(settings.key, setting.key)
            )
          )
          .limit(1);

        if (existingSetting.length > 0) {
          // Update existing setting
          await db
            .update(settings)
            .set({
              value: setting.value,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(settings.fid, fid),
                eq(settings.key, setting.key)
              )
            );
        } else {
          // Create new setting
          await db.insert(settings).values({
            fid,
            key: setting.key,
            value: setting.value,
            name: getSettingDisplayName(setting.key),
            type: getSettingType(setting.key),
            status: 1,
            createdAt: new Date(),
          });
        }
      }
    }

    console.log(`Updated location settings for FID: ${fid}`);
  } catch (error) {
    console.error(`Failed to update location settings for FID: ${fid}`, error);
    throw error;
  }
};

// Helper function to update location cache
const updateLocationCache = async (
  fid: string,
  fuid: string | undefined,
  locationData: UpdateLocationJobData['locationData']
): Promise<void> => {
  try {
    const { cacheService } = await import('../../config/redis');
    
    // Update location cache
    const locationCacheKey = `location:${fid}`;
    const cachedLocation = await cacheService.get(locationCacheKey) || {};
    
    if (locationData) {
      Object.assign(cachedLocation, locationData);
      cachedLocation.lastUpdated = new Date().toISOString();
      
      if (fuid) {
        cachedLocation.updatedBy = fuid;
      }
    }
    
    await cacheService.set(locationCacheKey, cachedLocation, 86400); // 24 hours TTL

    // Update location index for search
    if (locationData?.name || locationData?.address) {
      const searchKey = `location:search:${fid}`;
      const searchData = {
        fid,
        name: locationData.name || cachedLocation.name,
        address: locationData.address || cachedLocation.address,
        coordinates: locationData.latitude && locationData.longitude 
          ? `${locationData.latitude},${locationData.longitude}`
          : cachedLocation.coordinates,
        lastUpdated: new Date().toISOString(),
      };
      
      await cacheService.set(searchKey, searchData, 86400 * 7); // 7 days TTL
    }

    // Update location statistics
    const statsKey = `location:stats:${fid}`;
    const stats = await cacheService.get(statsKey) || {
      updateCount: 0,
      firstUpdate: new Date().toISOString(),
    };
    
    stats.updateCount += 1;
    stats.lastUpdate = new Date().toISOString();
    
    if (fuid) {
      if (!stats.updatedBy) {
        stats.updatedBy = [];
      }
      if (!stats.updatedBy.includes(fuid)) {
        stats.updatedBy.push(fuid);
      }
    }
    
    await cacheService.set(statsKey, stats, 86400 * 30); // 30 days TTL

    console.log(`Updated location cache for FID: ${fid}`);
  } catch (error) {
    console.error(`Failed to update location cache for FID: ${fid}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Helper function to log location update event
const logLocationUpdate = async (
  fid: string,
  fuid: string | undefined,
  locationData: UpdateLocationJobData['locationData']
): Promise<void> => {
  try {
    const { logDB } = await import('../../config/database');
    const { events } = await import('../../models/schemas');
    
    await logDB.insert(events).values({
      fid,
      uid: fuid || null,
      eventType: 'location_update',
      eventData: JSON.stringify({
        updatedFields: locationData ? Object.keys(locationData).filter(key => locationData[key as keyof typeof locationData] !== undefined) : [],
        timestamp: new Date().toISOString(),
      }),
      createdAt: new Date(),
    });

    console.log(`Logged location update event for FID: ${fid}`);
  } catch (error) {
    console.error(`Failed to log location update event for FID: ${fid}`, error);
    // Don't throw error here to avoid failing the main job
  }
};

// Helper function to get setting display name
const getSettingDisplayName = (key: string): string => {
  const displayNames: Record<string, string> = {
    location_name: 'Location Name',
    location_address: 'Address',
    location_latitude: 'Latitude',
    location_longitude: 'Longitude',
    location_phone: 'Phone Number',
    location_email: 'Email Address',
    location_website: 'Website URL',
    location_description: 'Description',
  };
  
  return displayNames[key] || key;
};

// Helper function to get setting type
const getSettingType = (key: string): string => {
  const types: Record<string, string> = {
    location_name: 'string',
    location_address: 'string',
    location_latitude: 'number',
    location_longitude: 'number',
    location_phone: 'string',
    location_email: 'email',
    location_website: 'url',
    location_description: 'string',
  };
  
  return types[key] || 'string';
};

// Job failure handler
export const handleUpdateLocationJobFailure = async (job: Job<UpdateLocationJobData>, error: Error): Promise<void> => {
  const { fid, fuid } = job.data;
  console.error(`Update location job failed permanently: ${fid}/${fuid}`, {
    error: error.message,
    stack: error.stack,
    attempts: job.attemptsMade,
    data: job.data,
  });

  // Could send notification or log to external service here
};

// Job completion handler
export const handleUpdateLocationJobCompletion = async (job: Job<UpdateLocationJobData>): Promise<void> => {
  const { fid, fuid } = job.data;
  console.log(`Update location job completed successfully: ${fid}/${fuid}`);
  
  // Could update metrics or send notifications here
};
