# =============================================================================
# FUS CMS Environment Configuration Template
# =============================================================================
# Copy this file to .env and update the values according to your environment
# Never commit .env file to version control

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PORT=3000
APP_NAME=FUS-CMS
APP_VERSION=2.0.0
APP_URL=http://localhost:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Main Database (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=fus_cms
DB_SSL=false
DB_TIMEZONE=+07:00

# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Log Database (for events and analytics)
LOG_DB_HOST=localhost
LOG_DB_PORT=3306
LOG_DB_USER=your_log_db_user
LOG_DB_PASSWORD=your_log_db_password
LOG_DB_NAME=fus_cms_logs
LOG_DB_SSL=false

# Database URLs (alternative to individual settings)
DATABASE_URL=mysql://your_db_user:your_db_password@localhost:3306/fus_cms
LOG_DATABASE_URL=mysql://your_log_db_user:your_log_db_password@localhost:3306/fus_cms_logs

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=fus-cms:
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY_ON_FAILOVER=100
REDIS_MAX_RETRIES_PER_REQUEST=3

# Redis URL (alternative to individual settings)
REDIS_URL=redis://localhost:6379

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=fus-cms
JWT_AUDIENCE=fus-cms-api
JWT_ALGORITHM=HS256

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Password Requirements
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
PASSWORD_HISTORY_LIMIT=5

# API Security
API_SECRET_KEY=your-api-secret-key-for-request-signing
WEBHOOK_SECRET=your-webhook-secret-key
SIGNATURE_ALGORITHM=sha256
SIGNATURE_TIMESTAMP_TOLERANCE=300

# CORS Settings
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-API-Key,X-Signature,X-Timestamp

# =============================================================================
# RATE LIMITING
# =============================================================================
# Global Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=false
RATE_LIMIT_SKIP_FAILED=false

# Authentication Rate Limiting
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5
AUTH_RATE_LIMIT_SKIP_SUCCESSFUL=true

# API Rate Limiting
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX_REQUESTS=100

# Upload Rate Limiting
UPLOAD_RATE_LIMIT_WINDOW_MS=3600000
UPLOAD_RATE_LIMIT_MAX_REQUESTS=10

# Password Reset Rate Limiting
PASSWORD_RESET_RATE_LIMIT_WINDOW_MS=3600000
PASSWORD_RESET_RATE_LIMIT_MAX_REQUESTS=3

# =============================================================================
# CACHE SETTINGS
# =============================================================================
CACHE_TTL_DEFAULT=3600
CACHE_TTL_SHORT=900
CACHE_TTL_LONG=7200
CACHE_TTL_VERY_LONG=86400

# Cache Keys TTL (in seconds)
CACHE_TTL_GAMES_LIST=1800
CACHE_TTL_GAMES_DETAIL=3600
CACHE_TTL_BANNERS_LIST=1800
CACHE_TTL_USER_SESSION=900
CACHE_TTL_ANALYTICS=1800

# =============================================================================
# QUEUE CONFIGURATION (BullMQ)
# =============================================================================
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_DB=1
QUEUE_REDIS_KEY_PREFIX=fus-cms:bull:

# Queue Settings
QUEUE_DEFAULT_JOB_ATTEMPTS=3
QUEUE_DEFAULT_JOB_BACKOFF_TYPE=exponential
QUEUE_DEFAULT_JOB_BACKOFF_DELAY=2000
QUEUE_DEFAULT_REMOVE_ON_COMPLETE=10
QUEUE_DEFAULT_REMOVE_ON_FAIL=5

# Worker Settings
QUEUE_WORKER_CONCURRENCY=5
QUEUE_WORKER_MAX_STALLED_COUNT=1
QUEUE_WORKER_STALLED_INTERVAL=30000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DATE_PATTERN=YYYY-MM-DD-HH
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Log Files
LOG_FILE_ERROR=logs/error.log
LOG_FILE_COMBINED=logs/combined.log
LOG_FILE_ACCESS=logs/access.log

# External Logging (optional)
LOG_EXTERNAL_ENABLED=false
LOG_EXTERNAL_URL=
LOG_EXTERNAL_API_KEY=

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================
UPLOAD_MAX_FILE_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,application/pdf
UPLOAD_DESTINATION=uploads
UPLOAD_TEMP_DIR=temp
UPLOAD_PUBLIC_URL=http://localhost:3000/uploads

# Cloud Storage (optional)
CLOUD_STORAGE_ENABLED=false
CLOUD_STORAGE_PROVIDER=aws
CLOUD_STORAGE_BUCKET=your-bucket-name
CLOUD_STORAGE_REGION=us-east-1
CLOUD_STORAGE_ACCESS_KEY=
CLOUD_STORAGE_SECRET_KEY=
CLOUD_STORAGE_CDN_URL=

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_ENABLED=false
EMAIL_PROVIDER=smtp
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM_NAME=FUS CMS
EMAIL_FROM_ADDRESS=<EMAIL>

# Email Templates
EMAIL_TEMPLATE_DIR=templates/emails
EMAIL_WELCOME_TEMPLATE=welcome
EMAIL_PASSWORD_RESET_TEMPLATE=password-reset
EMAIL_VERIFICATION_TEMPLATE=email-verification

# =============================================================================
# EXTERNAL API INTEGRATIONS
# =============================================================================
# RSS Parser Settings
RSS_PARSER_TIMEOUT=10000
RSS_PARSER_MAX_REDIRECTS=5
RSS_PARSER_USER_AGENT=FUS-CMS-RSS-Parser/2.0

# Third-party APIs
GOOGLE_ANALYTICS_ID=
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=
TWITTER_API_KEY=
TWITTER_API_SECRET=

# =============================================================================
# MONITORING & HEALTH CHECK
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_ENDPOINT=/health-check
HEALTH_CHECK_DATABASE=true
HEALTH_CHECK_REDIS=true
HEALTH_CHECK_QUEUES=true

# Metrics Collection
METRICS_ENABLED=false
METRICS_ENDPOINT=/metrics
METRICS_COLLECT_DEFAULT=true
METRICS_COLLECT_HTTP=true
METRICS_COLLECT_SYSTEM=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Debug Settings
DEBUG_ENABLED=true
DEBUG_SQL_QUERIES=false
DEBUG_CACHE_OPERATIONS=false
DEBUG_QUEUE_JOBS=false
DEBUG_API_REQUESTS=false

# Development Tools
DEV_TOOLS_ENABLED=true
API_DOCS_ENABLED=true
API_DOCS_ENDPOINT=/api/docs
QUEUE_DASHBOARD_ENABLED=true
QUEUE_DASHBOARD_ENDPOINT=/admin/queues

# Hot Reload
HOT_RELOAD_ENABLED=true
WATCH_FILES=true
WATCH_EXTENSIONS=.ts,.js,.json

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test Database
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password
TEST_DB_NAME=fus_cms_test

# Test Settings
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80
TEST_PARALLEL=true
TEST_MAX_WORKERS=4

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# SSL/TLS
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=
SSL_CA_PATH=

# Clustering
CLUSTER_ENABLED=false
CLUSTER_WORKERS=0

# Process Management
PM2_ENABLED=false
PM2_INSTANCES=max
PM2_EXEC_MODE=cluster

# =============================================================================
# BACKUP SETTINGS
# =============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_DESTINATION=backups
BACKUP_COMPRESS=true

# Database Backup
DB_BACKUP_ENABLED=false
DB_BACKUP_SCHEDULE=0 3 * * *
DB_BACKUP_RETENTION_DAYS=7

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_ANALYTICS_ENABLED=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_ADVANCED_CACHING=true
FEATURE_QUEUE_DASHBOARD=true
FEATURE_API_VERSIONING=true
FEATURE_REQUEST_SIGNING=false
FEATURE_RATE_LIMITING=true
FEATURE_AUDIT_LOGGING=true

# =============================================================================
# CUSTOM SETTINGS
# =============================================================================
# Add your custom environment variables here
CUSTOM_SETTING_1=value1
CUSTOM_SETTING_2=value2
