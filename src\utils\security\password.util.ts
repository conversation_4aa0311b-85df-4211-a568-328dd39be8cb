import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { BadRequestError } from '../errors/badRequest.error';

export class PasswordUtil {
  private static readonly SALT_ROUNDS = 12;
  private static readonly MIN_PASSWORD_LENGTH = 8;
  private static readonly MAX_PASSWORD_LENGTH = 128;

  // Hash password using bcrypt
  static async hashPassword(password: string): Promise<string> {
    try {
      this.validatePassword(password);
      const salt = await bcrypt.genSalt(this.SALT_ROUNDS);
      return await bcrypt.hash(password, salt);
    } catch (error) {
      throw new Error(`Password hashing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Verify password against hash
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('Password verification error:', error);
      return false;
    }
  }

  // Validate password strength
  static validatePassword(password: string): void {
    if (!password) {
      throw new BadRequestError('Password is required');
    }

    if (password.length < this.MIN_PASSWORD_LENGTH) {
      throw new BadRequestError(`Password must be at least ${this.MIN_PASSWORD_LENGTH} characters long`);
    }

    if (password.length > this.MAX_PASSWORD_LENGTH) {
      throw new BadRequestError(`Password must not exceed ${this.MAX_PASSWORD_LENGTH} characters`);
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      throw new BadRequestError('Password must contain at least one lowercase letter');
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      throw new BadRequestError('Password must contain at least one uppercase letter');
    }

    // Check for at least one digit
    if (!/\d/.test(password)) {
      throw new BadRequestError('Password must contain at least one number');
    }

    // Check for at least one special character
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      throw new BadRequestError('Password must contain at least one special character');
    }

    // Check for common weak passwords
    const commonPasswords = [
      'password', 'password123', '123456', '123456789', 'qwerty',
      'abc123', 'password1', 'admin', 'administrator', 'root',
      'user', 'guest', 'test', 'demo', 'welcome'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      throw new BadRequestError('Password is too common and easily guessable');
    }

    // Check for sequential characters
    if (this.hasSequentialChars(password)) {
      throw new BadRequestError('Password should not contain sequential characters');
    }

    // Check for repeated characters
    if (this.hasRepeatedChars(password)) {
      throw new BadRequestError('Password should not contain too many repeated characters');
    }
  }

  // Generate secure random password
  static generateSecurePassword(length: number = 16): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    
    let password = '';
    
    // Ensure at least one character from each category
    password += this.getRandomChar(lowercase);
    password += this.getRandomChar(uppercase);
    password += this.getRandomChar(numbers);
    password += this.getRandomChar(symbols);
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += this.getRandomChar(allChars);
    }
    
    // Shuffle the password
    return this.shuffleString(password);
  }

  // Generate password reset token
  static generateResetToken(): { token: string; hash: string } {
    const token = crypto.randomBytes(32).toString('hex');
    const hash = crypto.createHash('sha256').update(token).digest('hex');
    
    return { token, hash };
  }

  // Verify password reset token
  static verifyResetToken(token: string, hash: string): boolean {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    return crypto.timingSafeEqual(Buffer.from(tokenHash), Buffer.from(hash));
  }

  // Check password history (to prevent reuse)
  static async checkPasswordHistory(
    newPassword: string,
    passwordHistory: string[],
    historyLimit: number = 5
  ): Promise<boolean> {
    const recentPasswords = passwordHistory.slice(-historyLimit);
    
    for (const oldPasswordHash of recentPasswords) {
      if (await this.verifyPassword(newPassword, oldPasswordHash)) {
        return false; // Password was used recently
      }
    }
    
    return true; // Password is not in recent history
  }

  // Calculate password strength score (0-100)
  static calculatePasswordStrength(password: string): {
    score: number;
    feedback: string[];
  } {
    let score = 0;
    const feedback: string[] = [];

    // Length scoring
    if (password.length >= 8) score += 20;
    else feedback.push('Use at least 8 characters');

    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;

    // Character variety scoring
    if (/[a-z]/.test(password)) score += 15;
    else feedback.push('Add lowercase letters');

    if (/[A-Z]/.test(password)) score += 15;
    else feedback.push('Add uppercase letters');

    if (/\d/.test(password)) score += 15;
    else feedback.push('Add numbers');

    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 15;
    else feedback.push('Add special characters');

    // Complexity bonus
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) score += 10;
    else feedback.push('Avoid too many repeated characters');

    // Penalty for common patterns
    if (this.hasSequentialChars(password)) {
      score -= 10;
      feedback.push('Avoid sequential characters');
    }

    if (this.hasRepeatedChars(password)) {
      score -= 10;
      feedback.push('Avoid repeated character patterns');
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      feedback,
    };
  }

  // Private helper methods
  private static getRandomChar(chars: string): string {
    const randomIndex = crypto.randomInt(0, chars.length);
    return chars[randomIndex];
  }

  private static shuffleString(str: string): string {
    const arr = str.split('');
    for (let i = arr.length - 1; i > 0; i--) {
      const j = crypto.randomInt(0, i + 1);
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr.join('');
  }

  private static hasSequentialChars(password: string): boolean {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      '0123456789',
      'qwertyuiop',
      'asdfghjkl',
      'zxcvbnm'
    ];

    for (const sequence of sequences) {
      for (let i = 0; i <= sequence.length - 3; i++) {
        const subseq = sequence.substring(i, i + 3);
        if (password.includes(subseq) || password.includes(subseq.split('').reverse().join(''))) {
          return true;
        }
      }
    }

    return false;
  }

  private static hasRepeatedChars(password: string): boolean {
    // Check for 3 or more consecutive identical characters
    for (let i = 0; i <= password.length - 3; i++) {
      if (password[i] === password[i + 1] && password[i] === password[i + 2]) {
        return true;
      }
    }

    // Check if more than 50% of characters are the same
    const charCount: Record<string, number> = {};
    for (const char of password) {
      charCount[char] = (charCount[char] || 0) + 1;
    }

    const maxCount = Math.max(...Object.values(charCount));
    return maxCount > password.length * 0.5;
  }
}
