import { APIError } from './base.error';

export class TooManyRequestsError extends APIError {
  public readonly statusCode = 429;
  public readonly isOperational = true;

  constructor(message: string = 'Too many requests') {
    super(message);
    
    // Set the prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, TooManyRequestsError.prototype);
  }

  public toJSON() {
    return {
      status: 'error',
      code: this.statusCode,
      message: this.message,
    };
  }
}
