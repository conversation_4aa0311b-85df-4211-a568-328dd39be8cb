import { defineConfig } from 'drizzle-kit';
import * as dotenv from 'dotenv';

dotenv.config();

export default defineConfig({
  schema: './src/models/schemas/*.ts',
  out: './src/models/migrations',
  dialect: 'mysql',
  dbCredentials: {
    host: process.env.MYSQL_HOST!,
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USERNAME!,
    password: process.env.MYSQL_PASSWORD!,
    database: process.env.MYSQL_DB!,
  },
  verbose: true,
  strict: true,
});
